#!/usr/bin/env python3
"""
测试分析工具的基本功能
"""

import sys
import os
import pandas as pd
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta

def test_dolphindb_connection(analyzer):
    """测试DolphinDB连接"""
    assert analyzer.session is not None

def test_data_retrieval(analyzer):
    """测试数据获取"""
    try:
        # 先获取所有数据，不限制时间范围
        trade_data = analyzer.get_trade_data("BTCUSDT")
        print(f"✅ 获取交易数据成功: {len(trade_data)} 条记录")

        if len(trade_data) > 0:
            print(f"📊 数据时间范围: {trade_data['timestamp'].min()} 到 {trade_data['timestamp'].max()}")
            print(f"💰 价格范围: {trade_data['price'].min():.2f} - {trade_data['price'].max():.2f}")

            # 测试最近1小时的数据
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            recent_data = analyzer.get_trade_data("BTCUSDT", start_time, end_time)
            print(f"📈 最近1小时数据: {len(recent_data)} 条记录")

        return trade_data
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return None

def test_kline_generation(analyzer):
    """测试K线生成"""
    try:
        # 使用数据的实际时间范围
        start_time = datetime(2025, 8, 15, 12, 0, 0)  # 从数据开始时间
        end_time = datetime(2025, 8, 19, 5, 0, 0)    # 到数据结束时间

        # 测试不同时间间隔
        intervals = ['1m', '5m']

        for interval in intervals:
            indicator_data = analyzer.calculate_technical_indicators_dolphindb(
                "BTCUSDT", interval, start_time, end_time
            )

            if indicator_data and not indicator_data['klines'].empty:
                klines = indicator_data['klines']
                has_indicators = indicator_data['has_indicators']
                print(f"✅ {interval} K线生成成功: {len(klines)} 根")
                print(f"📈 技术指标: {'已计算' if has_indicators else '未计算'}")

                if len(klines) > 0:
                    print(f"   最新价格: {klines['close'].iloc[-1]:.2f}")
                    print(f"   价格范围: {klines['low'].min():.2f} - {klines['high'].max():.2f}")
                    print(f"   成交量: {klines['volume'].sum():.4f}")
            else:
                print(f"❌ {interval} K线生成失败")

    except Exception as e:
        print(f"❌ K线生成测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试Binance数据分析工具")
    print("=" * 50)
    
    # 测试DolphinDB连接
    analyzer = test_dolphindb_connection()
    if not analyzer:
        return
    
    print("\n📊 测试数据获取...")
    trade_data = test_data_retrieval(analyzer)
    
    if trade_data is not None and len(trade_data) > 0:
        print("\n📈 测试K线生成...")
        test_kline_generation(analyzer)
        
        print("\n✅ 所有测试完成！")
        print("\n🎯 启动Streamlit应用:")
        print("   source bi-scalp_venv/bin/activate")
        print("   streamlit run analyse_data.py")
    else:
        print("\n⚠️ 没有足够的数据进行测试")
        print("请确保 binance_data.py 正在运行并收集数据")

if __name__ == "__main__":
    main()
