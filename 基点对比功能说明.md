# 📍 基点对比功能使用说明

## 功能概述

新增的基点对比功能允许用户通过滑杆选择器精确定位一个基点，然后在鼠标悬停时实时显示当前点相对于基点的涨跌幅度。

## 功能特点

### ✨ 主要特性
- **滑杆精确选择**：使用滑杆选择器精确定位基点位置
- **实时涨跌幅显示**：鼠标悬停时在悬浮信息框底部显示相对涨跌幅
- **可视化基点标记**：基点在图表上显示为黄色星形标记
- **详细基点信息**：显示基点的价格和时间信息
- **启用/禁用控制**：可以随时启用或禁用基点对比功能

### 📊 显示信息
- **基点价格**：选中基点的价格
- **基点时间**：选中基点的时间
- **实时涨跌幅**：鼠标悬停点相对于基点的百分比变化

## 使用方法

### 🎯 基本操作流程

1. **启动应用**
   ```bash
   source venv_bi-scalp/bin/activate
   streamlit run analyse_data.py
   ```

2. **添加图层**
   - 在左侧边栏选择要显示的图层类型（K线、价格线等）
   - 点击"➕ 添加到图表"按钮
   - 建议先添加"📦 载入常用预设"来快速开始

3. **启用基点功能**
   - 在左侧边栏找到"📍 基点选择器"部分
   - 勾选"启用基点对比"复选框

4. **选择基点**
   - 在主界面会出现"📍 基点选择"区域
   - 使用滑杆"选择基点位置"来精确定位基点
   - 滑杆范围从0到数据点总数-1

5. **查看基点信息**
   - 基点价格和时间会显示在滑杆下方
   - 图表上会出现黄色星形标记表示基点位置

6. **查看实时对比**
   - 将鼠标悬停在图表上的任意点
   - 悬浮信息框底部会显示：
     - 📍 基点价格
     - 📊 当前点相对于基点的涨跌幅

### 🔄 功能控制

- **清除基点**：点击"🧹 清除基点"按钮重置基点选择
- **禁用功能**：取消勾选"启用基点对比"来禁用整个功能

## 技术实现

### 📦 核心功能
- **数据收集**：自动从第一个价格相关图层收集数据
- **滑杆控制**：使用Streamlit滑杆组件进行精确选择
- **状态管理**：通过 `st.session_state` 管理基点状态
- **悬浮信息定制**：动态修改Plotly图表的悬浮信息模板

### 🎨 可视化元素
- **黄色星形标记**：基点在图表上的标记
- **红色边框**：星形标记的边框，提高可见性
- **悬浮信息增强**：在原有悬浮信息基础上添加基点对比信息

## 数据源优先级

系统会按以下优先级选择数据源：

1. **真实价格线**：如果存在，使用原始交易数据的价格
2. **K线数据**：使用K线的收盘价
3. **价格线**：使用价格线数据

## 使用场景

### 💡 实际应用

1. **趋势分析**
   - 选择重要支撑位或阻力位作为基点
   - 观察价格相对于关键位置的变化

2. **波动分析**
   - 选择某个时间点作为基点
   - 分析后续价格的波动幅度

3. **收益计算**
   - 选择买入点作为基点
   - 实时查看潜在收益或损失

4. **风险评估**
   - 选择止损位作为基点
   - 监控价格偏离程度

### 📈 分析技巧

- **选择关键点位**：选择明显的高点、低点或重要技术位作为基点
- **结合技术指标**：在有技术指标的图表上选择信号点作为基点
- **多时间周期分析**：在不同时间间隔的图层上使用基点对比
- **动态调整**：根据市场变化及时调整基点位置

## 故障排除

### ❗ 常见问题

1. **滑杆不显示**
   - 确保已启用基点对比功能
   - 确保至少添加了一个价格相关的图层（K线、价格线、真实价格线）

2. **基点标记不显示**
   - 检查基点时间是否在当前图表的时间范围内
   - 尝试清除基点重新选择

3. **悬浮信息没有涨跌幅**
   - 确保已选择基点且基点价格不为0
   - 检查鼠标是否悬停在有数据的区域

### 🔧 性能优化

- **数据量控制**：大数据集时滑杆可能响应较慢，建议缩小时间范围
- **图层优化**：过多图层可能影响悬浮信息的显示效果

## 更新日志

### v1.0.0 (2025-09-17)
- ✅ 实现滑杆基点选择功能
- ✅ 添加基点可视化标记
- ✅ 集成悬浮信息涨跌幅显示
- ✅ 完善启用/禁用控制
- ✅ 添加基点信息展示

---

**注意**：此功能需要至少添加一个包含价格数据的图层（K线、价格线或真实价格线）才能正常工作。建议先使用"📦 载入常用预设"来快速开始。
