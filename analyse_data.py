#!/usr/bin/env python3
"""
Binance数据分析工具 - 从DolphinDB读取数据并进行技术分析
支持多时间周期的K线图、技术指标分析
run:
source venv_bi-scalp/bin/activate && streamlit run analyse_data.py
"""

import dolphindb as ddb
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from datetime import datetime, timedelta
import streamlit as st
from typing import Dict, List, Optional, Tuple

class DolphinDBAnalyzer:
    """DolphinDB数据分析器"""

    def __init__(self, host: str = "localhost", port: int = 8848, table_name: str = "shared_agg_trade_data_prd"):
        self.host = host
        self.port = port
        self.table_name = table_name
        self.session = None
        self.connect()

    def set_table_name(self, table_name: str):
        self.table_name = table_name

    def connect(self):
        """连接到DolphinDB"""
        try:
            self.session = ddb.session()
            self.session.connect(self.host, self.port, "", "")
            print(f"✅ 已连接到DolphinDB: {self.host}:{self.port}")
        except Exception as e:
            print(f"❌ 连接DolphinDB失败: {e}")
            raise

    def get_time_range(self, symbol: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """获取指定交易对的最早和最晚时间戳"""
        try:
            query = (
                f"select min(timestamp) as min_ts, max(timestamp) as max_ts "
                f"from {self.table_name} where symbol='{symbol}'"
            )
            result = self.session.run(query)
            if result is None or len(result) == 0:
                return None, None

            df = pd.DataFrame(result)
            if df.empty:
                return None, None

            min_ts = df.iloc[0].get('min_ts')
            max_ts = df.iloc[0].get('max_ts')

            def _to_datetime(value):
                if pd.isna(value):
                    return None
                if isinstance(value, pd.Timestamp):
                    return value.to_pydatetime()
                if isinstance(value, datetime):
                    return value
                if isinstance(value, (np.integer, np.floating, int, float)):
                    converted = pd.to_datetime(value, unit='ms', errors='coerce')
                else:
                    converted = pd.to_datetime(value, errors='coerce')
                return None if pd.isna(converted) else converted.to_pydatetime()

            return _to_datetime(min_ts), _to_datetime(max_ts)
        except Exception as e:
            print(f"⚠️ 获取时间范围失败: {e}")
            return None, None

    def get_trade_data(self, symbol: str = "BTCUSDT",
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None) -> pd.DataFrame:
        """获取交易数据"""
        try:
            # 构建查询条件
            where_clause = f"symbol='{symbol}'"
            if start_time:
                # 转换为Unix时间戳（毫秒）
                start_timestamp = int(start_time.timestamp() * 1000)
                where_clause += f" and timestamp >= {start_timestamp}"
            if end_time:
                # 转换为Unix时间戳（毫秒）
                end_timestamp = int(end_time.timestamp() * 1000)
                where_clause += f" and timestamp <= {end_timestamp}"

            query = f"""
                select * from {self.table_name}
                where {where_clause}
                order by timestamp
            """

            result = self.session.run(query)
            if len(result) == 0:
                print(f"⚠️ 没有找到 {symbol} 的数据")
                return pd.DataFrame()

            print(f"✅ 获取到 {len(result)} 条交易数据")
            return result

        except Exception as e:
            print(f"❌ 获取交易数据失败: {e}")
            return pd.DataFrame()

    def resample_to_klines_dolphindb(self, symbol: str, interval: str,
                                     start_time: Optional[datetime] = None,
                                     end_time: Optional[datetime] = None) -> pd.DataFrame:
        """使用DolphinDB直接生成K线数据"""
        try:
            # 构建查询条件
            where_clause = f"symbol='{symbol}'"
            if start_time:
                # 转换为Unix时间戳（毫秒）
                start_timestamp = int(start_time.timestamp() * 1000)
                where_clause += f" and timestamp >= {start_timestamp}"
            if end_time:
                # 转换为Unix时间戳（毫秒）
                end_timestamp = int(end_time.timestamp() * 1000)
                where_clause += f" and timestamp <= {end_timestamp}"

            # DolphinDB时间间隔映射
            interval_map = {
                '1s': '1s', '3s': '3s', '5s': '5s', '10s': '10s', '15s': '15s', '30s': '30s',
                '1m': '1m', '3m': '3m', '5m': '5m', '10m': '10m', '15m': '15m', '30m': '30m',
                '1h': '1h', '2h': '2h', '3h': '3h', '5h': '5h', '10h': '10h',
                '1d': '1d', '2d': '2d', '3d': '3d', '5d': '5d', '10d': '10d'
            }

            ddb_interval = interval_map.get(interval, '1m')

            # 使用DolphinDB生成K线（select 中仅使用聚合；时间用 first(timestamp) 代表该 bar 起点）
            query_with_buy = f"""
                select
                    first(timestamp) as timestamp,
                    first(price) as open,
                    max(price) as high,
                    min(price) as low,
                    last(price) as close,
                    sum(quantity) as volume,
                    sum(iif(is_buyer_maker, quantity, 0.0)) as buy_volume,
                    count(*) as trade_count
                from {self.table_name}
                where {where_clause}
                group by bar(timestamp, {ddb_interval})
                order by timestamp
            """

            query_basic = f"""
                select
                    first(timestamp) as timestamp,
                    first(price) as open,
                    max(price) as high,
                    min(price) as low,
                    last(price) as close,
                    sum(quantity) as volume,
                    count(*) as trade_count
                from {self.table_name}
                where {where_clause}
                group by bar(timestamp, {ddb_interval})
                order by timestamp
            """

            try:
                result = self.session.run(query_with_buy)
            except Exception as e:
                print(f"⚠️ 带买方成交量的K线查询失败，改用基础查询: {e}")
                try:
                    result = self.session.run(query_basic)
                except Exception as base_err:
                    print(f"❌ 生成K线失败: {base_err}")
                    return pd.DataFrame()
            if len(result) == 0:
                print(f"⚠️ 没有找到 {symbol} {interval} 的K线数据")
                return pd.DataFrame()

            print(f"✅ 生成 {len(result)} 根 {interval} K线")
            return result

        except Exception as e:
            print(f"❌ 生成K线失败: {e}")
            return pd.DataFrame()

    def calculate_technical_indicators_dolphindb(self, symbol: str, interval: str,
                                               start_time: Optional[datetime] = None,
                                               end_time: Optional[datetime] = None) -> Dict:
        """使用DolphinDB计算技术指标"""
        try:
            # 构建查询条件
            where_clause = f"symbol='{symbol}'"
            if start_time:
                # 转换为Unix时间戳（毫秒）
                start_timestamp = int(start_time.timestamp() * 1000)
                where_clause += f" and timestamp >= {start_timestamp}"
            if end_time:
                # 转换为Unix时间戳（毫秒）
                end_timestamp = int(end_time.timestamp() * 1000)
                where_clause += f" and timestamp <= {end_timestamp}"

            interval_map = {
                '5s': '5s', '10s': '10s', '15s': '15s', '30s': '30s',
                '1m': '1m', '3m': '3m', '5m': '5m', '15m': '15m',
                '1h': '1H', '1d': '1D'
            }

            ddb_interval = interval_map.get(interval, '1m')

            # 使用DolphinDB生成基本K线数据
            query = f"""
                select
                    first(timestamp) as timestamp,
                    first(price) as open,
                    max(price) as high,
                    min(price) as low,
                    last(price) as close,
                    sum(quantity) as volume,
                    count(*) as trade_count
                from {self.table_name}
                where {where_clause}
                group by bar(timestamp, {ddb_interval})
                order by timestamp
            """

            result = self.session.run(query)
            if len(result) == 0:
                return {}

            print(f"✅ 生成K线数据完成: {len(result)} 个数据点")
            return {
                'klines': result,
                'has_indicators': False  # 将在Python中计算技术指标
            }

        except Exception as e:
            print(f"❌ 计算技术指标失败: {e}")
            # 如果DolphinDB内建函数失败，返回基础K线数据
            klines = self.resample_to_klines_dolphindb(symbol, interval, start_time, end_time)
            return {
                'klines': klines,
                'has_indicators': False
            }

class TechnicalIndicators:
    """技术指标计算器 - 使用pandas简单实现"""

    @staticmethod
    def calculate_sma(data: pd.Series, period: int = 20) -> pd.Series:
        """简单移动平均线"""
        return data.rolling(window=period).mean()

    @staticmethod
    def calculate_ema(data: pd.Series, period: int = 20) -> pd.Series:
        """指数移动平均线"""
        return data.ewm(span=period).mean()

    @staticmethod
    def calculate_macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple:
        """MACD指标"""
        ema_fast = data.ewm(span=fast).mean()
        ema_slow = data.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram

    @staticmethod
    def calculate_rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """RSI指标"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    @staticmethod
    def calculate_bollinger_bands(data: pd.Series, period: int = 20, std: int = 2) -> Tuple:
        """布林带"""
        sma = data.rolling(window=period).mean()
        std_dev = data.rolling(window=period).std()
        upper = sma + (std_dev * std)
        lower = sma - (std_dev * std)
        return upper, sma, lower

    @staticmethod
    def calculate_psar(high: pd.Series, low: pd.Series, step: float = 0.02, max_step: float = 0.2) -> pd.Series:
        """Parabolic SAR 指标
        简化实现，使用高低价与迭代更新，返回一条 SAR 曲线。
        """
        n = len(high)
        if n == 0:
            return pd.Series(dtype=float)
        psar = [None] * n
        # 初始趋势：根据前两根K线收盘/高低判断，默认看涨
        trend_up = True
        if n >= 2:
            trend_up = high.iloc[1] + low.iloc[1] >= high.iloc[0] + low.iloc[0]
        af = step
        ep = high.iloc[0] if trend_up else low.iloc[0]
        psar_val = low.iloc[0] if trend_up else high.iloc[0]
        psar[0] = psar_val
        for i in range(1, n):
            # 计算当前 psar 预测值
            psar_val = psar_val + af * (ep - psar_val)
            # 趋势内对 psar 做极值限制（避免穿越前两根极值）
            if trend_up:
                # 不超过前两根最低价
                prev_low1 = low.iloc[i-1]
                prev_low2 = low.iloc[i-2] if i >= 2 else prev_low1
                psar_val = min(psar_val, prev_low1, prev_low2)
                # 若跌破，则趋势翻转
                if low.iloc[i] < psar_val:
                    trend_up = False
                    psar_val = ep
                    ep = low.iloc[i]
                    af = step
                else:
                    # 更新极值与加速因子
                    if high.iloc[i] > ep:
                        ep = high.iloc[i]
                        af = min(af + step, max_step)
            else:
                # 不低于前两根最高价（空头时 SAR 在上方）
                prev_high1 = high.iloc[i-1]
                prev_high2 = high.iloc[i-2] if i >= 2 else prev_high1
                psar_val = max(psar_val, prev_high1, prev_high2)
                # 若上破，则趋势翻转
                if high.iloc[i] > psar_val:
                    trend_up = True
                    psar_val = ep
                    ep = high.iloc[i]
                    af = step
                else:
                    if low.iloc[i] < ep:
                        ep = low.iloc[i]
                        af = min(af + step, max_step)
            psar[i] = psar_val
        return pd.Series(psar, index=high.index, dtype=float)


    @staticmethod
    def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Average True Range (ATR) = EMA(TR, period)
        TR = max(high-low, abs(high-prev_close), abs(low-prev_close))
        """
        prev_close = close.shift(1)
        tr1 = (high - low).abs()
        tr2 = (high - prev_close).abs()
        tr3 = (low - prev_close).abs()
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.ewm(span=period, adjust=False).mean()
        return atr

    @staticmethod
    def calculate_keltner_channels(high: pd.Series, low: pd.Series, close: pd.Series,
                                   period: int = 20, multiplier: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Keltner Channels: middle = EMA(close, period),
        upper = middle + multiplier*ATR(period), lower = middle - multiplier*ATR(period)
        返回 (upper, middle, lower)
        """
        middle = close.ewm(span=period, adjust=False).mean()
        atr = TechnicalIndicators.calculate_atr(high, low, close, period)
        upper = middle + multiplier * atr
        lower = middle - multiplier * atr
        return upper, middle, lower

    @staticmethod
    def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """ADX 指标（返回 ADX, +DI, -DI）
        采用Wilder方法的平滑近似（EMA）
        """
        prev_high = high.shift(1)
        prev_low = low.shift(1)
        prev_close = close.shift(1)

        up_move = high - prev_high
        down_move = prev_low - low
        plus_dm = ((up_move > down_move) & (up_move > 0)).astype(float) * up_move
        minus_dm = ((down_move > up_move) & (down_move > 0)).astype(float) * down_move

        tr = pd.concat([
            (high - low).abs(),
            (high - prev_close).abs(),
            (low - prev_close).abs()
        ], axis=1).max(axis=1)

        atr = tr.ewm(alpha=1/period, adjust=False).mean()
        plus_dm_sm = plus_dm.ewm(alpha=1/period, adjust=False).mean()
        minus_dm_sm = minus_dm.ewm(alpha=1/period, adjust=False).mean()

        plus_di = 100 * (plus_dm_sm / atr.replace(0, pd.NA))
        minus_di = 100 * (minus_dm_sm / atr.replace(0, pd.NA))

        dx = (100 * (plus_di - minus_di).abs() / (plus_di + minus_di).replace(0, pd.NA)).fillna(0)
        adx = dx.ewm(alpha=1/period, adjust=False).mean()
        return adx, plus_di, minus_di

    @staticmethod
    def calculate_dma(close: pd.Series, short: int = 10, long: int = 50, m: int = 10) -> Tuple[pd.Series, pd.Series]:
        """DMA 指标（常见定义）
        DMA = SMA(CLOSE, short) - SMA(CLOSE, long)
        AMA = SMA(DMA, m)
        返回 (DMA, AMA)
        """
        sma_short = close.rolling(window=short).mean()
        sma_long = close.rolling(window=long).mean()
        dma = sma_short - sma_long
        ama = dma.rolling(window=m).mean()
        return dma, ama

    @staticmethod
    def calculate_ichimoku(high: pd.Series, low: pd.Series, close: pd.Series,
                            tenkan: int = 9, kijun: int = 26, senkou_b: int = 52, displacement: int = 26) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series, pd.Series]:
        """Ichimoku Cloud
        返回 (tenkan, kijun, senkou_a, senkou_b_shifted, chikou)
        其中 senkou_a/b 已向前(未来)平移 displacement，chikou 向后(过去)平移 displacement。
        """
        tenkan_sen = (high.rolling(window=tenkan).max() + low.rolling(window=tenkan).min()) / 2.0
        kijun_sen = (high.rolling(window=kijun).max() + low.rolling(window=kijun).min()) / 2.0
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2.0).shift(displacement)
        senkou_span_b = ((high.rolling(window=senkou_b).max() + low.rolling(window=senkou_b).min()) / 2.0).shift(displacement)
        chikou_span = close.shift(-displacement)
        return tenkan_sen, kijun_sen, senkou_span_a, senkou_span_b, chikou_span


class ChartCreator:
    """图表创建器"""

    def __init__(self):
        self.colors = {
            'up': '#00ff88',      # 上涨绿色
            'down': '#ff4444',    # 下跌红色
            'volume': '#1f77b4',  # 成交量蓝色
            'ma': '#ff7f0e',      # 均线橙色
            'macd': '#2ca02c',    # MACD绿色
            'signal': '#d62728',  # 信号线红色
            'rsi': '#9467bd'      # RSI紫色
        }

    def create_candlestick_chart(self, df: pd.DataFrame, title: str = "K线图") -> go.Figure:
        """创建K线图"""
        fig = go.Figure()

        # 添加K线
        fig.add_trace(go.Candlestick(
            x=df['timestamp'],
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name="K线",
            increasing_line_color=self.colors['up'],
            decreasing_line_color=self.colors['down']
        ))

        # 设置布局
        fig.update_layout(
            title=title,
            xaxis_title="时间",
            yaxis_title="价格",
            xaxis_rangeslider_visible=False,
            height=600,
            showlegend=True,
            hovermode='x unified'
        )

        return fig

    def create_line_chart(self, df: pd.DataFrame, title: str = "价格折线图") -> go.Figure:
        """创建价格折线图"""
        fig = go.Figure()

        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['close'],
            mode='lines',
            name='收盘价',
            line=dict(color='blue', width=1),
            hovertemplate='时间: %{x}<br>价格: %{y:.2f}<extra></extra>'
        ))

        fig.update_layout(
            title=title,
            xaxis_title="时间",
            yaxis_title="价格",
            height=400,
            hovermode='x unified'
        )

        return fig

    def add_technical_indicators(self, fig: go.Figure, df: pd.DataFrame,
                               indicators: List[str], has_ddb_indicators: bool = False) -> go.Figure:
        """添加技术指标到图表"""

        if 'SMA' in indicators:
            if has_ddb_indicators and 'sma20' in df.columns:
                # 使用DolphinDB计算的SMA
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=df['sma20'],
                    mode='lines',
                    name='SMA(20)',
                    line=dict(color='orange', width=1)
                ))
            else:
                # 使用pandas计算SMA
                sma20 = TechnicalIndicators.calculate_sma(df['close'], 20)
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=sma20,
                    mode='lines',
                    name='SMA(20)',
                    line=dict(color='orange', width=1)
                ))

        if 'EMA' in indicators:
            if has_ddb_indicators and 'ema12' in df.columns:
                # 使用DolphinDB计算的EMA
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=df['ema12'],
                    mode='lines',
                    name='EMA(12)',
                    line=dict(color='purple', width=1)
                ))
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=df['ema26'],
                    mode='lines',
                    name='EMA(26)',
                    line=dict(color='blue', width=1)
                ))
            else:
                # 使用pandas计算EMA
                ema20 = TechnicalIndicators.calculate_ema(df['close'], 20)
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=ema20,
                    mode='lines',
                    name='EMA(20)',
                    line=dict(color='purple', width=1)
                ))

        return fig

    def create_macd_chart(self, df: pd.DataFrame, title: str = "MACD",
                         has_ddb_indicators: bool = False) -> go.Figure:
        """创建MACD图表"""

        if has_ddb_indicators and all(col in df.columns for col in ['macd_line', 'macd_signal', 'macd_histogram']):
            # 使用DolphinDB计算的MACD
            macd = df['macd_line']
            signal = df['macd_signal']
            histogram = df['macd_histogram']
        else:
            # 使用pandas计算MACD
            macd, signal, histogram = TechnicalIndicators.calculate_macd(df['close'])

        fig = make_subplots(rows=2, cols=1,
                           subplot_titles=['MACD线', 'MACD柱状图'],
                           vertical_spacing=0.1,
                           row_heights=[0.7, 0.3])

        # MACD线和信号线
        fig.add_trace(go.Scatter(
            x=df['timestamp'], y=macd,
            mode='lines', name='MACD',
            line=dict(color=self.colors['macd']),
            hovertemplate='时间: %{x}<br>MACD: %{y:.4f}<extra></extra>'
        ), row=1, col=1)

        fig.add_trace(go.Scatter(
            x=df['timestamp'], y=signal,
            mode='lines', name='Signal',
            line=dict(color=self.colors['signal']),
            hovertemplate='时间: %{x}<br>Signal: %{y:.4f}<extra></extra>'
        ), row=1, col=1)

        # MACD柱状图
        colors = ['red' if h < 0 else 'green' for h in histogram if not pd.isna(h)]
        fig.add_trace(go.Bar(
            x=df['timestamp'], y=histogram,
            name='Histogram',
            marker_color=colors,
            hovertemplate='时间: %{x}<br>Histogram: %{y:.4f}<extra></extra>'
        ), row=2, col=1)

        fig.update_layout(
            title=title,
            height=500,
            hovermode='x unified',
            showlegend=True
        )

        return fig

    def create_rsi_chart(self, df: pd.DataFrame, title: str = "RSI",
                        has_ddb_indicators: bool = False) -> go.Figure:
        """创建RSI图表"""

        if has_ddb_indicators and 'rsi' in df.columns:
            # 使用DolphinDB计算的RSI
            rsi = df['rsi']
        else:
            # 使用pandas计算RSI
            rsi = TechnicalIndicators.calculate_rsi(df['close'])

        fig = go.Figure()

        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=rsi,
            mode='lines',
            name='RSI(14)',
            line=dict(color=self.colors['rsi']),
            hovertemplate='时间: %{x}<br>RSI: %{y:.2f}<extra></extra>'
        ))

        # 添加超买超卖线
        fig.add_hline(y=70, line_dash="dash", line_color="red", annotation_text="超买(70)")
        fig.add_hline(y=30, line_dash="dash", line_color="green", annotation_text="超卖(30)")
        fig.add_hline(y=50, line_dash="dot", line_color="gray", annotation_text="中线(50)")

        fig.update_layout(
            title=title,
            xaxis_title="时间",
            yaxis_title="RSI",
            height=300,
            yaxis=dict(range=[0, 100]),
            hovermode='x unified',
            showlegend=True
        )

        return fig

def main():
    """主函数 - Streamlit应用"""
    st.set_page_config(
        page_title="Binance数据分析",
        page_icon="📈",
        layout="wide"
    )

    st.title("📈 Binance数据技术分析工具")
    st.markdown("---")

    # 初始化分析器
    try:
        analyzer = DolphinDBAnalyzer()
        chart_creator = ChartCreator()
    except Exception as e:
        st.error(f"连接DolphinDB失败: {e}")
        return

    # 侧边栏配置
    st.sidebar.header("📊 分析配置")

    # 显示设置
    st.sidebar.markdown("---")
    st.sidebar.subheader("显示设置")

    # 透明度设置 - 提供滑杆和数字输入两种方式
    opacity_method = st.sidebar.radio("透明度设置方式", ["滑杆", "手工输入"], horizontal=True)

    if opacity_method == "滑杆":
        hover_opacity = st.sidebar.slider("信息框透明度", min_value=0.0, max_value=1.0, value=0.75, step=0.05)
    else:
        hover_opacity = st.sidebar.number_input(
            "信息框透明度 (0.0-1.0)",
            min_value=0.0,
            max_value=1.0,
            value=0.75,
            step=0.01,
            format="%.2f"
        )

    st.sidebar.caption(f"当前透明度: {hover_opacity:.2f} (0.0=完全透明, 1.0=完全不透明)")

    # 符号选择（从库中读取可用符号）
    try:
        symbols_df = pd.DataFrame(analyzer.session.run(f"select distinct symbol from {analyzer.table_name}"))
        symbols = sorted(symbols_df['symbol'].tolist()) if not symbols_df.empty else ["BTCUSDT", "ETHUSDT"]
    except Exception:
        symbols = ["BTCUSDT", "ETHUSDT"]
    kc_period = 20
    kc_multiplier = 2.0

    selected_symbol = st.sidebar.selectbox("交易对", symbols, index=(symbols.index("BTCUSDT") if "BTCUSDT" in symbols else 0))

    st.sidebar.markdown("---")
    st.sidebar.subheader("时间范围")

    data_start, data_end = analyzer.get_time_range(selected_symbol)
    if data_start and data_end:
        st.sidebar.caption(
            f"可用数据范围: {data_start:%Y-%m-%d %H:%M:%S} → {data_end:%Y-%m-%d %H:%M:%S} (UTC)"
        )
    else:
        st.sidebar.caption("无法获取可用时间范围，默认使用最近6小时。")

    time_mode = st.sidebar.radio("时间模式", ["最近N小时", "自定义时间段"], index=0, horizontal=True)
    hours_back = None
    custom_start = None
    custom_end = None

    if time_mode == "最近N小时":
        hours_back = st.sidebar.selectbox("数据范围(小时)", [1, 2, 6, 12, 24, 48], index=2)
    else:
        default_end = data_end if data_end else datetime.utcnow()
        if data_start and default_end < data_start:
            default_end = data_start
        default_start = default_end - timedelta(hours=6)
        if data_start:
            default_start = max(default_start, data_start)
        if default_start > default_end:
            default_start = default_end

        # 确保日期默认值在有效范围内
        start_date_min = data_start.date() if data_start else default_start.date()
        end_date_max = data_end.date() if data_end else default_end.date()
        if start_date_min > end_date_max:
            start_date_min = end_date_max

        default_start_date = min(max(default_start.date(), start_date_min), end_date_max)
        default_end_date = min(max(default_end.date(), start_date_min), end_date_max)

        start_date = st.sidebar.date_input(
            "开始日期",
            value=default_start_date,
            min_value=start_date_min,
            max_value=end_date_max
        )
        
        # 开始时间输入 - 提供时间选择器和手工输入两种方式
        start_time_method = st.sidebar.radio("开始时间输入方式", ["时间选择器", "手工输入"], horizontal=True)
        
        if start_time_method == "时间选择器":
            start_time_input = st.sidebar.time_input("开始时间", value=default_start.time())
        else:
            start_time_str = default_start.time().strftime("%H:%M:%S")
            start_time_text = st.sidebar.text_input("开始时间 (HH:MM:SS)", value=start_time_str)
            try:
                # 解析手工输入的时间
                hours, minutes, seconds = map(int, start_time_text.split(':'))
                from datetime import time
                start_time_input = time(hours, minutes, seconds)
            except (ValueError, AttributeError):
                # 如果解析失败，使用默认时间
                st.sidebar.warning("时间格式无效，请使用 HH:MM:SS 格式")
                start_time_input = default_start.time()

        end_date = st.sidebar.date_input(
            "结束日期",
            value=max(start_date, default_end_date),
            min_value=start_date,
            max_value=end_date_max
        )
        
        # 结束时间输入 - 提供时间选择器和手工输入两种方式
        end_time_method = st.sidebar.radio("结束时间输入方式", ["时间选择器", "手工输入"], horizontal=True)
        
        if end_time_method == "时间选择器":
            end_time_input = st.sidebar.time_input("结束时间", value=default_end.time())
        else:
            end_time_str = default_end.time().strftime("%H:%M:%S")
            end_time_text = st.sidebar.text_input("结束时间 (HH:MM:SS)", value=end_time_str)
            try:
                # 解析手工输入的时间
                hours, minutes, seconds = map(int, end_time_text.split(':'))
                from datetime import time
                end_time_input = time(hours, minutes, seconds)
            except (ValueError, AttributeError):
                # 如果解析失败，使用默认时间
                st.sidebar.warning("时间格式无效，请使用 HH:MM:SS 格式")
                end_time_input = default_end.time()

        custom_start = datetime.combine(start_date, start_time_input)
        custom_end = datetime.combine(end_date, end_time_input)

        if data_start:
            custom_start = max(custom_start, data_start)
            custom_end = max(custom_end, data_start)
        if data_end:
            if custom_start > data_end:
                st.sidebar.warning("开始时间晚于最新数据，已自动调整。")
                custom_start = data_end
            custom_end = min(custom_end, data_end)
        if custom_end < custom_start:
            st.sidebar.warning("结束时间早于开始时间，已自动调整。")
            custom_end = custom_start

    # 叠加模式（单图显示）配置
    st.sidebar.markdown("---")
    st.sidebar.subheader("叠加图层配置")

    all_intervals = ['1s','3s','5s','10s','15s','30s','1m','3m','5m','10m','15m','30m','1h','2h','3h','5h','10h','1d','2d','3d','5d','10d']
    layer_type = st.sidebar.selectbox("选择要添加的类型", ["K线", "价格线", "真实价格线", "SMA", "EMA", "BOLL", "MACD", "RSI", "ADX", "ATR", "Range", "DMA", "Ichimoku", "Volume", "SAR", "KC"], index=0)

    # 真实价格线不需要时间间隔选择
    if layer_type == "真实价格线":
        layer_interval = "原始"
        st.sidebar.info("真实价格线显示每笔聚合交易的实际价格，无需选择时间间隔")
    else:
        layer_interval = st.sidebar.selectbox("间隔", all_intervals, index=6)  # 默认1m

    # 参数设置
    sma_periods = []
    ema_periods = []
    macd_fast = 12
    macd_slow = 26
    macd_signal = 9
    rsi_period = 14

    adx_period = 14

    sar_step = 0.02
    sar_max = 0.20

    boll_period = 20
    boll_std = 2

    if layer_type == "SMA":
        sma_periods = st.sidebar.multiselect("SMA周期", options=[3, 6, 12, 20, 30, 60, 120], default=[6, 12, 20])
        if st.sidebar.checkbox("自定义SMA周期"):
            custom_sma = st.sidebar.text_input("输入逗号分隔的周期", value="6,12,20")
            try:
                sma_periods = [int(x.strip()) for x in custom_sma.split(',') if x.strip()]
            except Exception:
                pass
    elif layer_type == "EMA":
        ema_periods = st.sidebar.multiselect("EMA周期", options=[3, 6, 12, 20, 26, 30, 60, 120], default=[12, 26])
        if st.sidebar.checkbox("自定义EMA周期"):
            custom_ema = st.sidebar.text_input("输入逗号分隔的周期", value="12,26")
            try:
                ema_periods = [int(x.strip()) for x in custom_ema.split(',') if x.strip()]
            except Exception:
                pass
    elif layer_type == "BOLL":
        boll_period = int(st.sidebar.number_input("BOLL周期", min_value=1, max_value=500, value=20))
        boll_std = int(st.sidebar.number_input("标准差倍数(std)", min_value=1, max_value=6, value=2))

    elif layer_type == "MACD":
        colm1, colm2, colm3 = st.sidebar.columns(3)
        macd_fast = colm1.number_input("快线", min_value=1, max_value=50, value=12)
        macd_slow = colm2.number_input("慢线", min_value=2, max_value=100, value=26)
        macd_signal = colm3.number_input("信号", min_value=1, max_value=50, value=9)
    elif layer_type == "SAR":
        col_s1, col_s2 = st.sidebar.columns(2)
        sar_step = float(col_s1.number_input("步长(step)", min_value=0.001, max_value=1.0, value=0.02, step=0.001, format="%.3f"))
        sar_max  = float(col_s2.number_input("最大步长(max)", min_value=0.01, max_value=2.0, value=0.20, step=0.01, format="%.2f"))
        if sar_max < sar_step:
            st.sidebar.warning("最大步长不应小于步长；已自动纠正")
            sar_max = sar_step
    elif layer_type == "RSI":
        rsi_period = st.sidebar.number_input("RSI周期", min_value=2, max_value=100, value=14)
    elif layer_type == "KC":
        kc_period = int(st.sidebar.number_input("KC周期", min_value=1, max_value=500, value=20))
        kc_multiplier = float(st.sidebar.number_input("KC倍数(mult)", min_value=0.1, max_value=10.0, value=2.0, step=0.1, format="%.1f"))
    elif layer_type == "ADX":
        adx_period = int(st.sidebar.number_input("ADX周期", min_value=2, max_value=100, value=14))

    elif layer_type == "ATR":
        atr_period = int(st.sidebar.number_input("ATR周期", min_value=2, max_value=200, value=14))

    elif layer_type == "Volume":
        volume_ma = int(st.sidebar.number_input("成交量MA周期(0为不显示)", min_value=0, max_value=500, value=0))

    elif layer_type == "DMA":
        dma_short = int(st.sidebar.number_input("DMA短期(N1)", min_value=1, max_value=500, value=10))
        dma_long = int(st.sidebar.number_input("DMA长期(N2)", min_value=1, max_value=500, value=50))
        dma_m = int(st.sidebar.number_input("DMA平滑(M)", min_value=1, max_value=500, value=10))

    elif layer_type == "Ichimoku":
        col_i1, col_i2 = st.sidebar.columns(2)
        ichi_tenkan = int(col_i1.number_input("Tenkan(转换线)", min_value=2, max_value=200, value=9))
        ichi_kijun  = int(col_i2.number_input("Kijun(基准线)", min_value=2, max_value=200, value=26))
        col_i3, col_i4 = st.sidebar.columns(2)
        ichi_span_b = int(col_i3.number_input("SenkouB(后移)", min_value=2, max_value=300, value=52))
        ichi_disp   = int(col_i4.number_input("位移(Displacement)", min_value=1, max_value=200, value=26))

    elif layer_type == "Range":
        range_ma = int(st.sidebar.number_input("Range均线MA(0为不显示)", min_value=0, max_value=500, value=0))


    # 初始化会话状态存储图层列表
    if "layers" not in st.session_state:
        st.session_state.layers = []

    # 初始化点击信息的会话状态
    if "click_info" not in st.session_state:
        st.session_state.click_info = {
            "enabled": False,
            "price": None,
            "time": None,
            "last_click": None
        }

    # 添加图层按钮
    if st.sidebar.button("➕ 添加到图表"):
        layer = {
            "type": layer_type,
            "interval": layer_interval,
            "symbol": selected_symbol,
        }
        if layer_type == "SMA":
            layer["periods"] = sma_periods or [20]
        if layer_type == "EMA":
            layer["periods"] = ema_periods or [20]
        if layer_type == "MACD":
            layer.update({"fast": int(macd_fast), "slow": int(macd_slow), "signal": int(macd_signal)})
        if layer_type == "RSI":
            layer.update({"period": int(rsi_period)})
        if layer_type == "SAR":
            layer.update({"step": float(sar_step), "max_step": float(sar_max)})
        if layer_type == "BOLL":
            layer.update({"period": int(boll_period), "std": int(boll_std)})
        if layer_type == "KC":
            layer.update({"period": int(kc_period), "multiplier": float(kc_multiplier)})
        if layer_type == "ADX":
            layer.update({"period": int(adx_period)})
        if layer_type == "ATR":
            layer.update({"period": int(atr_period)})
        if layer_type == "Volume":
            layer.update({"ma": int(volume_ma)})
        if layer_type == "DMA":
            layer.update({"short": int(dma_short), "long": int(dma_long), "m": int(dma_m)})
        if layer_type == "Ichimoku":
            layer.update({
                "tenkan": int(ichi_tenkan),
                "kijun": int(ichi_kijun),
                "senkou_b": int(ichi_span_b),
                "displacement": int(ichi_disp),
            })
        if layer_type == "Range":
            layer.update({"ma": int(range_ma)})

        st.session_state.layers.append(layer)

    # 清空图层
    if st.sidebar.button("🧹 清空图层"):
        st.session_state.layers = []

    # 已添加图层展示与删除
    if st.session_state.layers:
        st.sidebar.markdown("已添加图层：")
        for i, lyr in enumerate(list(st.session_state.layers)):
            # 为真实价格线显示特殊信息
            if lyr['type'] == '真实价格线':
                layer_info = f"{i+1}. {lyr['type']} @ {lyr['symbol']} (每笔交易)"
            elif lyr['type'] == 'SAR':
                layer_info = f"{i+1}. SAR @ {lyr['interval']} (step={lyr.get('step', 0.02)}, max={lyr.get('max_step', 0.2)})"
            elif lyr['type'] == 'KC':
                layer_info = f"{i+1}. KC @ {lyr['interval']} (p={lyr.get('period', 20)}, m={lyr.get('multiplier', 2.0)})"
            elif lyr['type'] == 'BOLL':
                layer_info = f"{i+1}. BOLL @ {lyr['interval']} (p={lyr.get('period', 20)}, std={lyr.get('std', 2)})"
            elif lyr['type'] == 'ADX':
                layer_info = f"{i+1}. ADX @ {lyr['interval']} (p={lyr.get('period', 14)})"
            elif lyr['type'] == 'ATR':
                layer_info = f"{i+1}. ATR @ {lyr['interval']} (p={lyr.get('period', 14)})"
            elif lyr['type'] == 'Volume':
                layer_info = f"{i+1}. Volume @ {lyr['interval']} (ma={lyr.get('ma', 0)})"
            elif lyr['type'] == 'DMA':
                layer_info = f"{i+1}. DMA @ {lyr['interval']} (N1={lyr.get('short',10)}, N2={lyr.get('long',50)}, M={lyr.get('m',10)})"
            elif lyr['type'] == 'Ichimoku':
                layer_info = f"{i+1}. Ichimoku @ {lyr['interval']} (T={lyr.get('tenkan',9)}, K={lyr.get('kijun',26)}, B={lyr.get('senkou_b',52)}, D={lyr.get('displacement',26)})"
            elif lyr['type'] == 'Range':
                layer_info = f"{i+1}. Range @ {lyr['interval']} (ma={lyr.get('ma', 0)})"
            else:
                layer_info = f"{i+1}. {lyr['type']} @ {lyr['interval']} ({lyr.get('periods', lyr.get('period', ''))})"
            st.sidebar.write(layer_info)
            if st.sidebar.button(f"删除第{i+1}层", key=f"del_{i}"):
                st.session_state.layers.pop(i)
                st.rerun()

    # 点击信息功能
    st.sidebar.markdown("---")
    st.sidebar.subheader("🖱️ 点击信息")

    # 启用/禁用点击信息功能
    click_info_enabled = st.sidebar.checkbox("启用点击获取价格", value=st.session_state.click_info["enabled"])
    st.session_state.click_info["enabled"] = click_info_enabled

    if click_info_enabled:
        st.sidebar.info("💡 点击图表获取该点的价格信息")

        # 显示最近点击的信息
        if st.session_state.click_info["price"] is not None:
            st.sidebar.success(f"🎯 点击价格: ${st.session_state.click_info['price']:.6f}")
            if st.session_state.click_info["time"]:
                st.sidebar.caption(f"⏰ 点击时间: {st.session_state.click_info['time']}")

        # 清除点击信息按钮
        if st.sidebar.button("🧹 清除点击信息", use_container_width=True):
            st.session_state.click_info.update({
                "price": None,
                "time": None,
                "last_click": None
            })
            st.success("✅ 点击信息已清除")
            st.rerun()
    else:
        # 如果禁用，清除点击信息
        if st.session_state.click_info["price"] is not None:
            st.session_state.click_info.update({
                "enabled": False,
                "price": None,
                "time": None,
                "last_click": None
            })

    if st.sidebar.button("🔄 刷新数据"):
        st.rerun()

    # 获取数据 - 先获取所有数据查看范围
    with st.spinner("正在获取数据..."):
        cached_all_trades = None
        effective_start = None
        effective_end = custom_end if (time_mode == "自定义时间段" and custom_end) else data_end

        if effective_end is None:
            cached_all_trades = analyzer.get_trade_data(selected_symbol)
            if not cached_all_trades.empty:
                cached_all_trades = cached_all_trades.copy()
                ts_series = cached_all_trades['timestamp']
                if np.issubdtype(ts_series.dtype, np.number):
                    ts_dt = pd.to_datetime(ts_series, unit='ms', errors='coerce')
                else:
                    ts_dt = pd.to_datetime(ts_series, errors='coerce')
                cached_all_trades['timestamp_dt'] = ts_dt
                valid_ts = ts_dt.dropna()
                if not valid_ts.empty:
                    if data_start is None:
                        data_start = valid_ts.min()
                    effective_end = valid_ts.max()
            else:
                trade_data = cached_all_trades

        if effective_end is not None:
            if time_mode == "自定义时间段" and custom_start:
                effective_start = custom_start
            else:
                window_hours = hours_back if hours_back is not None else 6
                effective_start = effective_end - timedelta(hours=window_hours)
            if data_start is not None:
                effective_start = max(effective_start, data_start)
            if effective_start > effective_end:
                st.warning("开始时间晚于结束时间，已自动调整到可用范围内。")
                effective_start = data_start if data_start is not None else effective_end

            if cached_all_trades is not None and not cached_all_trades.empty:
                mask = (cached_all_trades['timestamp_dt'] >= effective_start) & (
                    cached_all_trades['timestamp_dt'] <= effective_end
                )
                trade_data = cached_all_trades.loc[mask].drop(columns=['timestamp_dt'])
            else:
                trade_data = analyzer.get_trade_data(selected_symbol, effective_start, effective_end)
        else:
            # 若仍无法确定时间范围，则直接获取全部数据
            trade_data = analyzer.get_trade_data(selected_symbol)

    if trade_data.empty:
        st.warning("没有找到数据，请确保数据收集程序正在运行")
        return

    ts_series = trade_data['timestamp']
    if np.issubdtype(ts_series.dtype, np.number):
        ts_dt = pd.to_datetime(ts_series, unit='ms', errors='coerce')
    else:
        ts_dt = pd.to_datetime(ts_series, errors='coerce')

    if effective_start is None or pd.isna(effective_start):
        min_ts = ts_dt.min()
        effective_start = None if pd.isna(min_ts) else min_ts.to_pydatetime()
    elif isinstance(effective_start, pd.Timestamp):
        effective_start = effective_start.to_pydatetime()

    if effective_end is None or pd.isna(effective_end):
        max_ts = ts_dt.max()
        effective_end = None if pd.isna(max_ts) else max_ts.to_pydatetime()
    elif isinstance(effective_end, pd.Timestamp):
        effective_end = effective_end.to_pydatetime()

    if effective_start and effective_end:
        st.success(
            f"✅ 成功获取 {len(trade_data)} 条交易数据，范围 {effective_start:%Y-%m-%d %H:%M:%S} → {effective_end:%Y-%m-%d %H:%M:%S}"
        )
    else:
        st.success(f"✅ 成功获取 {len(trade_data)} 条交易数据")

    # 统一时间轴范围（根据当前选中symbol与数据范围）
    start_dt = pd.to_datetime(trade_data['timestamp'].min(), unit='ms', errors='coerce')
    end_dt = pd.to_datetime(trade_data['timestamp'].max(), unit='ms', errors='coerce')

    # 没有任何图层时，不再自动添加默认图层
    if not st.session_state.layers:
        st.info("尚未添加图层。请在左侧选择类型与间隔，点击‘➕ 添加到图表’；或点击‘📦 载入常用预设’快速开始。")

    # 一键载入常用预设按钮（可选）
    if st.sidebar.button("📦 载入常用预设"):
        st.session_state.layers = [
            {"type": "K线", "interval": "1m", "symbol": selected_symbol},
            {"type": "真实价格线", "interval": "原始", "symbol": selected_symbol},
            {"type": "SMA", "interval": "1m", "symbol": selected_symbol, "periods": [6, 12, 20]},
            {"type": "EMA", "interval": "1m", "symbol": selected_symbol, "periods": [12, 26]},
            {"type": "SAR", "interval": "1m", "symbol": selected_symbol, "step": 0.02, "max_step": 0.2},
            {"type": "MACD", "interval": "5m", "symbol": selected_symbol, "fast": 12, "slow": 26, "signal": 9},
            {"type": "RSI", "interval": "5m", "symbol": selected_symbol, "period": 14},
        ]
        st.rerun()

    # 缓存不同间隔的数据，避免重复查询
    @st.cache_data(show_spinner=False)
    def load_kline(symbol: str, interval: str, sdt: datetime, edt: datetime) -> pd.DataFrame:
        df = analyzer.resample_to_klines_dolphindb(symbol, interval, sdt, edt)
        if df is None or len(df) == 0:
            return pd.DataFrame()
        df = df.copy()
        # 转成datetime
        if np.issubdtype(df['timestamp'].dtype, np.number):
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', errors='coerce')
        else:
            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')

        # 处理买卖成交量及比例
        pandas_freq_map = {
            '1s': '1S', '3s': '3S', '5s': '5S', '10s': '10S', '15s': '15S', '30s': '30S',
            '1m': '1T', '3m': '3T', '5m': '5T', '10m': '10T', '15m': '15T', '30m': '30T',
            '1h': '1H', '2h': '2H', '3h': '3H', '5h': '5H', '10h': '10H',
            '1d': '1D', '2d': '2D', '3d': '3D', '5d': '5D', '10d': '10D'
        }

        if 'volume' in df.columns:
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
        else:
            df['volume'] = 0.0
        df['volume'] = df['volume'].fillna(0.0)

        df_index = df.index if hasattr(df, 'index') else pd.RangeIndex(len(df))
        if 'buy_volume' in df.columns:
            buy_series_raw = pd.to_numeric(df['buy_volume'], errors='coerce')
        else:
            buy_series_raw = pd.Series(np.nan, index=df_index, dtype=float)

        if buy_series_raw.isna().all():
            df['buy_volume'] = 0.0
            pandas_freq = pandas_freq_map.get(interval)
            if pandas_freq:
                raw_df = load_raw_trades(symbol, sdt, edt)
                required_cols = {'timestamp', 'quantity', 'is_buyer_maker'}
                if not raw_df.empty and required_cols.issubset(raw_df.columns):
                    raw_df = raw_df[['timestamp', 'quantity', 'is_buyer_maker']].copy()
                    if np.issubdtype(raw_df['timestamp'].dtype, np.number):
                        raw_df['timestamp'] = pd.to_datetime(raw_df['timestamp'], unit='ms', errors='coerce')
                    else:
                        raw_df['timestamp'] = pd.to_datetime(raw_df['timestamp'], errors='coerce')
                    raw_df.dropna(subset=['timestamp'], inplace=True)
                    raw_df['quantity'] = pd.to_numeric(raw_df['quantity'], errors='coerce').fillna(0.0)

                    buyer_col = raw_df['is_buyer_maker']
                    if pd.api.types.is_bool_dtype(buyer_col):
                        buyer_mask = buyer_col.fillna(False)
                    elif pd.api.types.is_numeric_dtype(buyer_col):
                        buyer_mask = buyer_col.fillna(0).astype(float) > 0
                    else:
                        buyer_mask = buyer_col.astype(str).str.lower().isin(['true', '1', 't', 'yes', 'y'])
                    buyer_mask = buyer_mask.fillna(False)

                    raw_df['bucket'] = raw_df['timestamp'].dt.floor(pandas_freq)
                    buy_map = raw_df.loc[buyer_mask, ['bucket', 'quantity']].groupby('bucket')['quantity'].sum()
                    df['bucket'] = df['timestamp'].dt.floor(pandas_freq)
                    df['buy_volume'] = df['bucket'].map(buy_map).fillna(0.0)
                    df.drop(columns=['bucket'], inplace=True, errors='ignore')
        else:
            df['buy_volume'] = buy_series_raw.fillna(0.0)

        df['buy_volume'] = pd.to_numeric(df['buy_volume'], errors='coerce').fillna(0.0)
        df['sell_volume'] = (df['volume'] - df['buy_volume']).clip(lower=0.0)
        with np.errstate(divide='ignore', invalid='ignore'):
            df['buy_ratio'] = np.where(df['volume'] > 0, df['buy_volume'] / df['volume'], np.nan)
        df['buy_ratio'] = pd.to_numeric(df['buy_ratio'], errors='coerce').clip(lower=0.0, upper=1.0)

        return df

    # 缓存原始交易数据
    @st.cache_data(show_spinner=False)
    def load_raw_trades(symbol: str, sdt: datetime, edt: datetime) -> pd.DataFrame:
        df = analyzer.get_trade_data(symbol, sdt, edt)
        if df is None or len(df) == 0:
            return pd.DataFrame()
        df = df.copy()
        # 转成datetime
        if np.issubdtype(df['timestamp'].dtype, np.number):
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', errors='coerce')
        else:
            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
        return df

    # 根据是否包含 MACD / RSI组(含RSI/ADX/ATR/DMA/Range) / Volume 决定 y 轴域的分布
    has_macd = any(l['type'] == 'MACD' for l in st.session_state.layers)
    has_adx = any(l['type'] == 'ADX' for l in st.session_state.layers)
    has_atr = any(l['type'] == 'ATR' for l in st.session_state.layers)
    has_dma = any(l['type'] == 'DMA' for l in st.session_state.layers)
    has_range = any(l['type'] == 'Range' for l in st.session_state.layers)
    has_rsi = any(l['type'] in ('RSI','ADX','ATR','DMA','Range') for l in st.session_state.layers)
    has_vol = any(l['type'] == 'Volume' for l in st.session_state.layers)

    # 初始化
    macd_domain = rsi_domain = vol_domain = None

    count_sub = int(has_macd) + int(has_rsi) + int(has_vol)
    if count_sub == 0:
        price_domain = [0.0, 1.0]
    elif count_sub == 1:
        if has_macd:
            price_domain = [0.3, 1.0]
            macd_domain = [0.0, 0.3]
        elif has_rsi:
            price_domain = [0.2, 1.0]
            rsi_domain = [0.0, 0.2]
        else:  # only volume
            price_domain = [0.2, 1.0]
            vol_domain = [0.0, 0.2]
    elif count_sub == 2:
        price_domain = [0.5, 1.0]
        bottom = [0.0, 0.2]; mid = [0.2, 0.5]
        # 底部优先放 Volume，其次 RSI，MACD 在上方
        if has_vol:
            vol_domain = bottom
            if has_rsi and has_macd:
                # 不会出现，因为count_sub==2
                pass
            elif has_rsi:
                rsi_domain = mid
            else:
                macd_domain = mid
        else:
            # 没有 Volume，底部放 RSI，MACD 在中间
            if has_rsi and has_macd:
                rsi_domain = bottom
                macd_domain = mid
            elif has_rsi:
                rsi_domain = bottom
            else:
                macd_domain = bottom
    else:  # count_sub == 3
        price_domain = [0.6, 1.0]
        vol_domain = [0.0, 0.2]
        rsi_domain = [0.2, 0.4]
        macd_domain = [0.4, 0.6]

    fig = go.Figure()

    # 颜色循环（通用）
    palette = [
        '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
    ]
    color_idx = 0

    volume_ratio_axis_needed = False

    # 为相同指标内的不同“范围/周期”提供不同颜色
    sma_palette = ['#ff7f0e', '#1f77b4', '#2ca02c', '#9467bd', '#8c564b', '#17becf']
    ema_palette = ['#e377c2', '#bcbd22', '#7f7f7f', '#d62728', '#1f77b4', '#2ca02c']

    # 收集全局SMA/EMA周期以保持相同周期的颜色一致
    sma_periods_all = []
    ema_periods_all = []
    for lyr in st.session_state.layers:
        if lyr.get('type') == 'SMA':
            sma_periods_all.extend(lyr.get('periods', [20]))
        if lyr.get('type') == 'EMA':
            ema_periods_all.extend(lyr.get('periods', [20]))
    sma_unique = sorted(set(sma_periods_all))
    ema_unique = sorted(set(ema_periods_all))
    sma_color_map = {p: sma_palette[i % len(sma_palette)] for i, p in enumerate(sma_unique)}
    ema_color_map = {p: ema_palette[i % len(ema_palette)] for i, p in enumerate(ema_unique)}

    # 绘制每个图层
    for layer in st.session_state.layers:
        ltype = layer['type']
        lint = layer['interval']
        sym = layer['symbol']
        df = load_kline(sym, lint, start_dt, end_dt)
        if df.empty:
            st.warning(f"{sym} @{lint} 数据不足，跳过 {ltype}")
            continue

        color = palette[color_idx % len(palette)]
        color_idx += 1

        # 价格区、MACD、RSI、Volume 对应的Y轴名称
        price_yaxis = "y"
        macd_yaxis = "y2"
        rsi_yaxis = "y3"
        volume_yaxis = "y4"

        if ltype == 'K线':
            fig.add_trace(go.Candlestick(
                x=df['timestamp'], open=df['open'], high=df['high'], low=df['low'], close=df['close'],
                name=f"K线@{lint}", increasing_line_color='#00cc88', decreasing_line_color='#ff4444',
                showlegend=True,
                yaxis=price_yaxis, # 绑定到价格Y轴
                xhoverformat='%Y-%m-%d %H:%M:%S',
                yhoverformat='.6f'
            ))
        elif ltype == '价格线':
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=df['close'], mode='lines', name=f"收盘价@{lint}",
                line=dict(color=color, width=1),
                yaxis=price_yaxis # 绑定到价格Y轴
            ))
        elif ltype == 'KC':
            period = int(layer.get('period', 20))
            mult = float(layer.get('multiplier', 2.0))
            upper, middle, lower = TechnicalIndicators.calculate_keltner_channels(
                df['high'], df['low'], df['close'], period=period, multiplier=mult)
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=middle, mode='lines', name=f"KC{period}@{lint}",
                line=dict(color=color, width=1), yaxis=price_yaxis
            ))
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=upper, mode='lines', name=f"KC upper@{lint}",
                line=dict(color=color, width=1, dash='dash'), yaxis=price_yaxis, opacity=0.8
            ))
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=lower, mode='lines', name=f"KC lower@{lint}",
                line=dict(color=color, width=1, dash='dash'), yaxis=price_yaxis, opacity=0.8
            ))
        elif ltype == 'BOLL':
            period = int(layer.get('period', 20))
            std = int(layer.get('std', 2))
            upper, middle, lower = TechnicalIndicators.calculate_bollinger_bands(
                df['close'], period=period, std=std)
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=middle, mode='lines', name=f"BOLL{period}@{lint}",
                line=dict(color=color, width=1), yaxis=price_yaxis
            ))
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=upper, mode='lines', name=f"BOLL upper@{lint}",
                line=dict(color=color, width=1, dash='dash'), yaxis=price_yaxis, opacity=0.8
            ))
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=lower, mode='lines', name=f"BOLL lower@{lint}",
                line=dict(color=color, width=1, dash='dash'), yaxis=price_yaxis, opacity=0.8
            ))
        elif ltype == '真实价格线':
            # 获取原始交易数据，显示每笔聚合交易的真实价格
            raw_df = load_raw_trades(sym, start_dt, end_dt)
            if not raw_df.empty:
                fig.add_trace(go.Scatter(
                    x=raw_df['timestamp'], y=raw_df['price'], mode='lines', name=f"真实价格@{sym}",
                    line=dict(color=color, width=0.5),  # 更细的线条，因为数据点更密集
                    hovertemplate='时间: %{x}<br>价格: %{y:.6f}<br>数量: %{customdata:.6f}<extra></extra>',
                    customdata=raw_df['quantity'],  # 显示交易数量
                    yaxis=price_yaxis # 绑定到价格Y轴
                ))
            else:
                st.warning(f"无法获取 {sym} 的原始交易数据")

        elif ltype == 'Ichimoku':
            t = int(layer.get('tenkan', 9))
            k = int(layer.get('kijun', 26))
            b = int(layer.get('senkou_b', 52))
            d = int(layer.get('displacement', 26))
            tenkan, kijun, senkou_a, senkou_b, chikou = TechnicalIndicators.calculate_ichimoku(
                df['high'], df['low'], df['close'], tenkan=t, kijun=k, senkou_b=b, displacement=d
            )
            # Tenkan & Kijun
            fig.add_trace(go.Scatter(x=df['timestamp'], y=tenkan, mode='lines',
                                     name=f"Tenkan{t}@{lint}", line=dict(color='#ff7f0e', width=1.5),
                                     yaxis=price_yaxis))
            fig.add_trace(go.Scatter(x=df['timestamp'], y=kijun, mode='lines',
                                     name=f"Kijun{k}@{lint}", line=dict(color='#1f77b4', width=1.5),
                                     yaxis=price_yaxis))
            # Cloud (Senkou A then B with fill)
            fig.add_trace(go.Scatter(x=df['timestamp'], y=senkou_a, mode='lines',
                                     name=f"SenkouA@{lint}", line=dict(color='rgba(34,197,94,0.7)', width=1),
                                     yaxis=price_yaxis))
            fig.add_trace(go.Scatter(x=df['timestamp'], y=senkou_b, mode='lines',
                                     name=f"SenkouB@{lint}", line=dict(color='rgba(239,68,68,0.7)', width=1),
                                     fill='tonexty', fillcolor='rgba(100,149,237,0.15)', yaxis=price_yaxis))
            # Chikou
            fig.add_trace(go.Scatter(x=df['timestamp'], y=chikou, mode='lines',
                                     name=f"Chikou@{lint}", line=dict(color='#2ca02c', width=1, dash='dot'),
                                     yaxis=price_yaxis))

        elif ltype == 'SMA':
            periods = layer.get('periods', [20])
            for p in periods:
                sma = TechnicalIndicators.calculate_sma(df['close'], p)
                color_p = sma_color_map.get(p, palette[(color_idx + p) % len(palette)])
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=sma, mode='lines', name=f"SMA{p}@{lint}",
                    line=dict(color=color_p, width=1, dash='solid'),
                    yaxis=price_yaxis # 绑定到价格Y轴
                ))
        elif ltype == 'EMA':
            periods = layer.get('periods', [20])
            for p in periods:
                ema = TechnicalIndicators.calculate_ema(df['close'], p)
                color_p = ema_color_map.get(p, palette[(color_idx + p) % len(palette)])
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=ema, mode='lines', name=f"EMA{p}@{lint}",
                    line=dict(color=color_p, width=1, dash='dot'),
                    yaxis=price_yaxis # 绑定到价格Y轴
                ))
        elif ltype == 'SAR':
            step = float(layer.get('step', 0.02))
            max_step = float(layer.get('max_step', 0.2))
            sar = TechnicalIndicators.calculate_psar(df['high'], df['low'], step=step, max_step=max_step)
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=sar, mode='markers', name=f"SAR@{lint}(s={step:.3f},m={max_step:.2f})",
                marker=dict(color=color, size=4, symbol='circle'),
                yaxis=price_yaxis # 绑定到价格Y轴
            ))

        elif ltype == 'MACD' and has_macd and macd_domain is not None:
            fast = layer.get('fast', 12); slow = layer.get('slow', 26); signal = layer.get('signal', 9)
            macd_line, signal_line, hist = TechnicalIndicators.calculate_macd(df['close'], fast, slow, signal)
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=macd_line, mode='lines', name=f"MACD@{lint}({fast},{slow},{signal})",
                line=dict(color=color),
                yaxis=macd_yaxis # 绑定到MACD Y轴
            ))
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=signal_line, mode='lines', name=f"Signal@{lint}",
                line=dict(color='#d62728'),
                yaxis=macd_yaxis # 绑定到MACD Y轴
            ))
            # 柱状
            bar_colors = ['#2ca02c' if v >= 0 else '#d62728' for v in hist.fillna(0)]
            fig.add_trace(go.Bar(
                x=df['timestamp'], y=hist, name=f"Hist@{lint}", marker_color=bar_colors,
                opacity=0.5,
                yaxis=macd_yaxis # 绑定到MACD Y轴
            ))

        elif ltype == 'Volume' and vol_domain is not None:
            # 颜色：收盘>=开盘为上涨色，否则下跌色
            up_mask = (df['close'] >= df['open']).fillna(False)
            bar_colors = ['#00cc88' if u else '#ff4444' for u in up_mask]
            fig.add_trace(go.Bar(
                x=df['timestamp'], y=df['volume'], name=f"VOL@{lint}",
                marker_color=bar_colors, opacity=0.6,
                yaxis=volume_yaxis
            ))
            ma = int(layer.get('ma', 0))
            if ma and ma > 0:
                vma = df['volume'].rolling(ma).mean()
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=vma, mode='lines', name=f"VMA{ma}@{lint}",
                    line=dict(color='#ff7f0e', width=1.5), yaxis=volume_yaxis
                ))
            if 'buy_volume' in df.columns:
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=df['buy_volume'], mode='lines', name=f"买方成交量@{lint}",
                    line=dict(color='#17becf', width=1.5), yaxis=volume_yaxis,
                    hovertemplate='时间: %{x}<br>买方成交量: %{y:,.4f}<extra></extra>'
                ))
            if 'sell_volume' in df.columns:
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=df['sell_volume'], mode='lines', name=f"卖方成交量@{lint}",
                    line=dict(color='#bcbd22', width=1.5), yaxis=volume_yaxis,
                    hovertemplate='时间: %{x}<br>卖方成交量: %{y:,.4f}<extra></extra>'
                ))
            if 'buy_ratio' in df.columns and df['buy_ratio'].notna().any():
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=df['buy_ratio'], mode='lines', name=f"买方成交比例@{lint}",
                    line=dict(color='#9467bd', width=1.5, dash='dot'), yaxis='y5',
                    hovertemplate='时间: %{x}<br>买方成交比例: %{y:.1%}<extra></extra>'
                ))
                volume_ratio_axis_needed = True
        elif ltype == 'RSI' and has_rsi and rsi_domain is not None:
            period = layer.get('period', 14)
            rsi = TechnicalIndicators.calculate_rsi(df['close'], period)
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=rsi, mode='lines', name=f"RSI{period}@{lint}",
                line=dict(color=color),
                yaxis=rsi_yaxis # 绑定到RSI Y轴
            ))

        elif ltype == 'ADX' and rsi_domain is not None:
            period = int(layer.get('period', 14))
            adx, _plus_di, _minus_di = TechnicalIndicators.calculate_adx(
                df['high'], df['low'], df['close'], period)
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=adx, mode='lines', name=f"ADX{period}@{lint}",
                line=dict(color=color),
                yaxis=rsi_yaxis # 绑定到RSI/ADX Y轴
            ))

        elif ltype == 'ATR' and rsi_domain is not None:
            period = int(layer.get('period', 14))
            atr = TechnicalIndicators.calculate_atr(df['high'], df['low'], df['close'], period)
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=atr, mode='lines', name=f"ATR{period}@{lint}",
                line=dict(color=color),
                yaxis=rsi_yaxis
            ))
        elif ltype == 'Range' and rsi_domain is not None:
            rng = (df['high'] - df['low']).abs()
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=rng, mode='lines', name=f"Range@{lint}",
                line=dict(color=color), yaxis=rsi_yaxis
            ))
            ma = int(layer.get('ma', 0))
            if ma and ma > 0:
                rma = rng.rolling(ma).mean()
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=rma, mode='lines', name=f"RMA{ma}@{lint}",
                    line=dict(color='#ff7f0e', width=1.5, dash='dash'), yaxis=rsi_yaxis
                ))


        elif ltype == 'DMA' and rsi_domain is not None:
            short = int(layer.get('short', 10))
            long = int(layer.get('long', 50))
            m = int(layer.get('m', 10))
            dma_line, ama_line = TechnicalIndicators.calculate_dma(df['close'], short, long, m)
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=dma_line, mode='lines', name=f"DMA{short}-{long}@{lint}",
                line=dict(color=color), yaxis=rsi_yaxis
            ))
            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=ama_line, mode='lines', name=f"AMA{m}@{lint}",
                line=dict(color='#ff7f0e', width=1.5, dash='dash'), yaxis=rsi_yaxis
            ))

    # 统一外观 + 十字线（跟随鼠标，纵线穿透所有子图）
    fig.update_xaxes(range=[start_dt, end_dt])

    # 为所有轴设置十字线
    fig.update_xaxes(
        showspikes=True,
        spikecolor="rgba(255,0,0,0.5)",
        spikesnap="cursor",
        spikemode="across",
        spikethickness=1,
        spikedash="solid"
    )

    # 构造 RSI 轴标题（根据已添加的 RSI/ADX/ATR/DMA 动态组合）
    rsi_title_parts = []
    if any(l['type'] == 'RSI' for l in st.session_state.layers):
        rsi_title_parts.append('RSI')
    if has_adx:
        rsi_title_parts.append('ADX')
    if has_atr:
        rsi_title_parts.append('ATR')
    if has_dma:
        rsi_title_parts.append('DMA')
    if has_range:
        rsi_title_parts.append('Range')
    rsi_title = '/'.join(rsi_title_parts) if rsi_title_parts else 'RSI'

    # 定义Y轴布局
    yaxis_config = {
        "yaxis": dict(
            domain=price_domain,
            title="价格",
            showspikes=True,
            spikecolor="rgba(0,0,255,0.5)",
            spikemode="across",
            spikesnap="cursor",
            spikethickness=1,
            spikedash="solid"
        ),
    }
    if has_macd and macd_domain:
        yaxis_config["yaxis2"] = dict(
            domain=macd_domain,
            title="MACD",
            showspikes=True,
            spikecolor="rgba(0,0,255,0.5)",
            spikemode="across",
            spikesnap="cursor",
            spikethickness=1,
            spikedash="solid"
        )
    if has_rsi and rsi_domain:
        yaxis_config["yaxis3"] = dict(
            domain=rsi_domain,
            title=rsi_title,
            showspikes=True,
            spikecolor="rgba(0,0,255,0.5)",
            spikemode="across",
            spikesnap="cursor",
            spikethickness=1,
            spikedash="solid"
        )
    if has_vol and vol_domain:
        yaxis_config["yaxis4"] = dict(
            domain=vol_domain,
            title="Volume",
            showspikes=True,
            spikecolor="rgba(0,0,255,0.5)",
            spikemode="across",
            spikesnap="cursor",
            spikethickness=1,
            spikedash="solid"
        )
        if volume_ratio_axis_needed:
            yaxis_config["yaxis5"] = dict(
                domain=vol_domain,
                title="买方成交比例",
                overlaying="y4",
                side="right",
                range=[0, 1],
                tickformat=".0%",
                showspikes=True,
                spikecolor="rgba(0,0,255,0.5)",
                spikemode="across",
                spikesnap="cursor",
                spikethickness=1,
                spikedash="solid"
            )


    # 自定义悬浮信息模板，包含点击信息
    if st.session_state.click_info["enabled"] and st.session_state.click_info["price"] is not None:
        click_price = st.session_state.click_info["price"]
        click_time = st.session_state.click_info["time"]

        # 为所有trace添加点击信息到悬浮模板
        for trace in fig.data:
            if hasattr(trace, 'hovertemplate') and trace.hovertemplate:
                # 在现有的hovertemplate基础上添加点击信息
                if '<extra></extra>' in trace.hovertemplate:
                    trace.hovertemplate = trace.hovertemplate.replace('<extra></extra>',
                        f'<br>🖱️ 点击价格: ${click_price:.6f}<br>⏰ 点击时间: {click_time}<extra></extra>')
                else:
                    trace.hovertemplate += f'<br>🖱️ 点击价格: ${click_price:.6f}<br>⏰ 点击时间: {click_time}'

    # 最终布局设置
    fig.update_layout(
        **yaxis_config,  # 应用Y轴布局
        title=f"叠加图 - 统一时间轴 ({selected_symbol})",
        height=1000,
        hovermode='x unified',
        hoversubplots='axis',
        hoverlabel=dict(bgcolor=f"rgba(0,0,0,{hover_opacity})", bordercolor='#999', font=dict(size=11, color='#fff')),
        xaxis_rangeslider_visible=False,
        legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='left', x=0),
        spikedistance=-1,
        hoverdistance=100
    )

    # 显示图表（始终使用普通图表，但在启用点击功能时添加事件处理）
    if st.session_state.click_info["enabled"]:
        try:
            from streamlit_plotly_events import plotly_events

            # 使用plotly_events显示图表并捕获点击事件
            selected_points = plotly_events(
                fig,
                click_event=True,
                hover_event=False,
                select_event=False,
                key="click_info_chart",
                override_height=1000,
                override_width="100%"
            )

            # 处理点击事件
            if selected_points and len(selected_points) > 0:
                clicked_point = selected_points[0]

                # 获取点击的坐标
                if 'x' in clicked_point and 'y' in clicked_point:
                    clicked_time = clicked_point['x']
                    clicked_price = float(clicked_point['y'])

                    # 格式化时间
                    try:
                        if isinstance(clicked_time, str):
                            clicked_datetime = pd.to_datetime(clicked_time)
                        else:
                            clicked_datetime = pd.to_datetime(clicked_time)

                        formatted_time = clicked_datetime.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        formatted_time = str(clicked_time)

                    # 更新点击信息
                    st.session_state.click_info.update({
                        "price": clicked_price,
                        "time": formatted_time,
                        "last_click": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })

                    st.success(f"✅ 已获取点击信息: ${clicked_price:.6f}")
                    st.rerun()

        except ImportError:
            # 如果没有安装streamlit-plotly-events，显示提示并使用普通图表
            st.sidebar.warning("⚠️ 需要安装 streamlit-plotly-events 来支持点击功能")
            st.sidebar.code("pip install streamlit-plotly-events")

            st.plotly_chart(fig, use_container_width=True, config={
                "scrollZoom": True,
                "displayModeBar": True
            })

        except Exception as e:
            # 如果出现其他错误，显示错误信息并回退到普通图表
            st.error(f"点击功能出现错误: {e}")
            st.plotly_chart(fig, use_container_width=True, config={
                "scrollZoom": True,
                "displayModeBar": True
            })
    else:
        # 如果未启用点击功能，使用普通图表
        st.plotly_chart(fig, use_container_width=True, config={
            "scrollZoom": True,
            "displayModeBar": True
        })

    # 可选：显示底部最近数据
    with st.expander("查看最近数据"):
        st.dataframe(trade_data.tail(50), use_container_width=True)

if __name__ == "__main__":
    main()
