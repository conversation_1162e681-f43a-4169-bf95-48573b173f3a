# Repository Guidelines

## Project Structure & Module Organization
`runbot.py` drives trading, `binance_data.py` ingests futures trades, and `analyse_data.py` plus `dashboard.py` power analytics and Streamlit views. Utility scripts (`get_account_info.py`, `simple_analyse.py`, `telegram_bot.py`) share the root directory—keep shared logic importable and avoid placing code solely under `__main__`. Pytest fixtures in `conftest.py` wrap the DolphinDB adapter defined in `analyse_data.py`, and the companion tests (`test_analyse.py`, `test_dolphindb.py`) consume it directly. Configuration templates (`.env.example`) and dependency pins (`requirements.txt`) stay at the project root; logs and CSV exports should remain untracked.

## Build, Test, and Development Commands
- `python -m venv .venv && source .venv/bin/activate` creates an isolated Python runtime.
- `pip install -r requirements.txt` installs Binance, DolphinDB, Streamlit, and analytics dependencies.
- `python binance_data.py --symbol BTCUSDT` streams raw trades into DolphinDB for analysis and tests.
- `python runbot.py --symbol BTCUSDC --quantity 0.001 --take_profit 50 --max-orders 50` starts the scalping bot with explicit parameters.
- `python dashboard.py --symbol BTCUSDC --refresh-rate 5` serves the monitoring UI.
- `pytest -q` runs the automated checks; narrow scope with `pytest test_analyse.py -k connection` when debugging database access.

## Coding Style & Naming Conventions
Follow PEP 8 with four-space indentation, `snake_case` functions, and `CamelCase` classes. Guard executable sections with `if __name__ == "__main__":` so modules remain importable. Favour explicit arguments over globals, extract shared constants (symbols, table names), and keep console output aligned with the existing Rich-style messaging.

## Testing Guidelines
Pytest discovers `test_*.py` in the repository root; mirror that convention for new coverage. The suite assumes a DolphinDB server on `localhost:8848` populated by `binance_data.py`, so seed data before running integration checks. Assert on DataFrame shapes, column names, and key aggregates to resist market-driven variance.

## Commit & Pull Request Guidelines
Commit subjects should stay under 72 characters and use an imperative tone similar to the current history (`fix merge error`, `Handle buy volume aggregation fallback`). Pull requests must record required configuration (.env keys, DolphinDB schema), note `pytest` results, and include screenshots or terminal output for dashboard-facing updates.

## Security & Configuration Tips
Never commit `.env` files or Binance credentials; store secrets locally or in CI vaults. Restrict API keys to the permissions the bot needs, rotate them regularly, and confirm ports and symbols via environment variables before exposing services.
