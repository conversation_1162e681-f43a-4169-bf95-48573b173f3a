# 🖱️ 点击信息功能使用说明

## 功能概述

新增的点击信息功能允许用户通过鼠标单击图表上的任意点来获取该点的价格信息，并将这些信息显示在悬浮信息窗的底部。

## 功能特点

### ✨ 主要特性
- **单击获取信息**：鼠标单击图表上任意位置获取价格信息
- **悬浮信息增强**：点击信息显示在所有悬浮信息窗的底部
- **实时更新**：每次点击都会更新显示的信息
- **侧边栏显示**：在侧边栏同时显示最近点击的信息
- **启用/禁用控制**：可以随时启用或禁用点击信息功能

### 📊 显示信息
- **点击价格**：点击位置的精确价格值
- **点击时间**：点击位置对应的时间点
- **悬浮增强**：在所有悬浮信息窗底部显示点击信息

## 使用方法

### 🎯 基本操作流程

1. **启动应用**
   ```bash
   source venv_bi-scalp/bin/activate
   streamlit run analyse_data.py
   ```

2. **添加图层**
   - 在左侧边栏选择要显示的图层类型
   - 点击"➕ 添加到图表"按钮
   - 建议使用"📦 载入常用预设"快速开始

3. **启用点击功能**
   - 在左侧边栏找到"🖱️ 点击信息"部分
   - 勾选"启用点击获取价格"复选框

4. **点击获取信息**
   - 在图表上任意位置单击鼠标
   - 系统会自动获取点击位置的价格和时间信息
   - 侧边栏会显示最新的点击信息

5. **查看悬浮信息**
   - 将鼠标悬停在图表上的任意点
   - 悬浮信息窗底部会显示：
     - 🖱️ 点击价格
     - ⏰ 点击时间

6. **管理点击信息**
   - 点击"🧹 清除点击信息"按钮清除当前信息
   - 取消勾选"启用点击获取价格"禁用功能

### 🔄 功能控制

- **启用功能**：勾选"启用点击获取价格"复选框
- **禁用功能**：取消勾选复选框，同时清除所有点击信息
- **清除信息**：点击"🧹 清除点击信息"按钮重置点击数据
- **实时更新**：每次点击都会自动更新显示的信息

## 技术实现

### 📦 核心功能
- **事件捕获**：使用 `streamlit-plotly-events` 捕获图表点击事件
- **状态管理**：通过 `st.session_state` 管理点击信息状态
- **悬浮信息定制**：动态修改Plotly图表的悬浮信息模板
- **坐标解析**：自动解析点击位置的坐标和时间信息

### 🎨 用户界面
- **侧边栏控制**：在侧边栏添加点击信息控制区域
- **信息展示**：在侧边栏显示最新的点击信息
- **悬浮增强**：在所有悬浮信息窗底部添加点击信息

## 使用场景

### 💡 实际应用

1. **价格记录**
   - 点击重要的价格点位进行记录
   - 快速获取关键时刻的价格信息

2. **技术分析**
   - 点击支撑位和阻力位获取精确价格
   - 记录突破点位的价格和时间

3. **交易决策**
   - 点击潜在的买入/卖出点位
   - 获取决策点的详细信息

4. **数据验证**
   - 验证图表上显示的价格数据
   - 确认特定时间点的价格准确性

### 📈 分析技巧

- **关键点位标记**：点击重要的技术分析点位进行标记
- **价格对比**：结合悬浮信息进行价格对比分析
- **时间节点记录**：记录重要事件发生时的价格信息
- **精确定位**：获取图表上任意点的精确价格数据

## 依赖要求

### 📦 必需包
- `streamlit-plotly-events`：用于捕获图表点击事件
- `plotly`：图表绘制和事件处理
- `pandas`：时间和数据处理

### 🔧 安装方法
```bash
pip install streamlit-plotly-events
```

## 故障排除

### ❗ 常见问题

1. **点击无响应**
   - 确保已启用"启用点击获取价格"功能
   - 检查是否已安装 `streamlit-plotly-events`
   - 确保点击的是图表数据区域

2. **悬浮信息不显示点击信息**
   - 确保已经点击过图表获取了点击信息
   - 检查点击信息是否已清除
   - 尝试重新点击图表

3. **时间格式异常**
   - 系统会自动处理时间格式转换
   - 如果出现错误，会显示原始时间字符串

### 🔧 性能优化

- **单次点击**：避免频繁点击，每次点击都会触发页面更新
- **数据范围**：在合适的数据范围内进行点击操作
- **功能控制**：不使用时可以禁用功能以提高性能

## 兼容性

### ✅ 支持的功能
- 与所有图层类型兼容（K线、价格线、技术指标等）
- 支持多时间周期的数据
- 兼容现有的悬浮信息系统

### ⚠️ 注意事项
- 需要安装 `streamlit-plotly-events` 包
- 点击事件会触发页面刷新
- 每次只保存最新的一次点击信息

## 更新日志

### v1.0.0 (2025-09-17)
- ✅ 实现鼠标点击获取价格信息功能
- ✅ 添加悬浮信息窗增强显示
- ✅ 集成 streamlit-plotly-events 事件处理
- ✅ 完善启用/禁用控制
- ✅ 添加侧边栏信息展示

---

**注意**：此功能需要 `streamlit-plotly-events` 包支持。如果未安装，系统会显示安装提示并回退到普通图表模式。
