#!/usr/bin/env python3
"""
Test DolphinDB connection and basic operations
"""

import dolphindb as ddb
import pandas as pd
from datetime import datetime

def test_dolphindb_connection():
    """Test basic DolphinDB connection and operations."""
    try:
        # Connect to DolphinDB
        session = ddb.session()
        session.connect("localhost", 8848, "", "")
        print("✅ Connected to DolphinDB successfully")
        
        # Test basic query
        result = session.run("1+1")
        print(f"✅ Basic query test: 1+1 = {result}")
        
        # Test in-memory table creation
        session.run("""
            try {
                select count(*) from test_table
                print("Test table already exists")
            } catch(ex) {
                test_table = table(
                    1:0,
                    `symbol`timestamp`price,
                    [SYMBOL, TIMESTAMP, DOUBLE]
                )
                share test_table as test_table
                print("Created test table")
            }
        """)

        # Test data insertion
        test_data = pd.DataFrame({
            'symbol': ['BTCUSDT'],
            'timestamp': [pd.Timestamp.now()],
            'price': [50000.0]
        })

        session.upload({'test_data': test_data})
        session.run("test_table.append!(test_data)")
        print("✅ Test data inserted successfully")

        # Test data query
        result = session.run("select * from test_table limit 5")
        print(f"✅ Query result: {result}")
        
        session.close()
        print("✅ DolphinDB test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ DolphinDB test failed: {e}")
        return False

if __name__ == "__main__":
    test_dolphindb_connection()
