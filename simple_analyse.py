#!/usr/bin/env python3
"""
简化版Binance数据分析工具 - 生成HTML报告
"""

import sys
import os
import pandas as pd
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_data import <PERSON>lphinDBAnalyzer, ChartCreator, TechnicalIndicators
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots

def create_analysis_report():
    """创建分析报告"""
    print("🚀 开始生成Binance数据分析报告")
    print("=" * 50)
    
    # 初始化分析器
    try:
        analyzer = DolphinDBAnalyzer()
        chart_creator = ChartCreator()
        print("✅ DolphinDB连接成功")
    except Exception as e:
        print(f"❌ DolphinDB连接失败: {e}")
        return
    
    # 获取数据
    print("\n📊 获取交易数据...")
    trade_data = analyzer.get_trade_data("BTCUSDT")
    
    if trade_data.empty:
        print("❌ 没有找到数据")
        return
    
    print(f"✅ 获取到 {len(trade_data)} 条交易数据")
    print(f"📊 数据时间范围: {trade_data['timestamp'].min()} 到 {trade_data['timestamp'].max()}")
    print(f"💰 价格范围: {trade_data['price'].min():.2f} - {trade_data['price'].max():.2f}")
    
    # 使用数据的实际时间范围
    start_time = trade_data['timestamp'].min()
    end_time = trade_data['timestamp'].max()
    
    # 创建多时间周期分析
    intervals = ['1m', '5m', '15m']
    figures = []
    
    for interval in intervals:
        print(f"\n📈 生成 {interval} 时间周期分析...")
        
        # 获取K线数据
        indicator_data = analyzer.calculate_technical_indicators_dolphindb(
            "BTCUSDT", interval, start_time, end_time
        )
        
        if not indicator_data or indicator_data['klines'].empty:
            print(f"⚠️ {interval} 数据不足")
            continue
        
        klines = indicator_data['klines']
        print(f"✅ 生成 {len(klines)} 根 {interval} K线")
        
        # 计算技术指标
        klines['sma20'] = TechnicalIndicators.calculate_sma(klines['close'], 20)
        klines['ema12'] = TechnicalIndicators.calculate_ema(klines['close'], 12)
        klines['ema26'] = TechnicalIndicators.calculate_ema(klines['close'], 26)
        macd, signal, histogram = TechnicalIndicators.calculate_macd(klines['close'])
        klines['macd'] = macd
        klines['signal'] = signal
        klines['histogram'] = histogram
        klines['rsi'] = TechnicalIndicators.calculate_rsi(klines['close'])
        
        # 创建综合图表
        fig = make_subplots(
            rows=4, cols=1,
            subplot_titles=[
                f'{interval} K线图 + 移动平均线',
                f'{interval} MACD',
                f'{interval} RSI',
                f'{interval} 成交量'
            ],
            vertical_spacing=0.05,
            row_heights=[0.5, 0.2, 0.15, 0.15]
        )
        
        # K线图
        fig.add_trace(go.Candlestick(
            x=klines['timestamp'],
            open=klines['open'],
            high=klines['high'],
            low=klines['low'],
            close=klines['close'],
            name=f"{interval} K线",
            increasing_line_color='#00ff88',
            decreasing_line_color='#ff4444'
        ), row=1, col=1)
        
        # 移动平均线
        fig.add_trace(go.Scatter(
            x=klines['timestamp'], y=klines['sma20'],
            mode='lines', name='SMA(20)',
            line=dict(color='orange', width=1)
        ), row=1, col=1)
        
        fig.add_trace(go.Scatter(
            x=klines['timestamp'], y=klines['ema12'],
            mode='lines', name='EMA(12)',
            line=dict(color='blue', width=1)
        ), row=1, col=1)
        
        # MACD
        fig.add_trace(go.Scatter(
            x=klines['timestamp'], y=klines['macd'],
            mode='lines', name='MACD',
            line=dict(color='green')
        ), row=2, col=1)
        
        fig.add_trace(go.Scatter(
            x=klines['timestamp'], y=klines['signal'],
            mode='lines', name='Signal',
            line=dict(color='red')
        ), row=2, col=1)
        
        # RSI
        fig.add_trace(go.Scatter(
            x=klines['timestamp'], y=klines['rsi'],
            mode='lines', name='RSI',
            line=dict(color='purple')
        ), row=3, col=1)
        
        # 添加RSI超买超卖线
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
        
        # 成交量
        fig.add_trace(go.Bar(
            x=klines['timestamp'], y=klines['volume'],
            name='成交量',
            marker_color='lightblue'
        ), row=4, col=1)
        
        # 更新布局
        fig.update_layout(
            title=f"BTCUSDT {interval} 技术分析",
            height=1000,
            showlegend=True,
            xaxis_rangeslider_visible=False
        )
        
        # 更新所有子图的x轴
        for i in range(1, 5):
            fig.update_xaxes(showgrid=True, row=i, col=1)
            fig.update_yaxes(showgrid=True, row=i, col=1)
        
        # 设置RSI范围
        fig.update_yaxes(range=[0, 100], row=3, col=1)
        
        figures.append((interval, fig))
    
    # 生成HTML报告
    print(f"\n📄 生成HTML报告...")
    
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Binance BTCUSDT 技术分析报告</title>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .chart-container { margin-bottom: 50px; }
            .stats { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>📈 Binance BTCUSDT 技术分析报告</h1>
            <p>生成时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
        </div>
        
        <div class="stats">
            <h3>📊 数据统计</h3>
            <p><strong>交易数据:</strong> """ + f"{len(trade_data):,}" + """ 条记录</p>
            <p><strong>时间范围:</strong> """ + f"{start_time} 到 {end_time}" + """</p>
            <p><strong>价格范围:</strong> $""" + f"{trade_data['price'].min():.2f} - ${trade_data['price'].max():.2f}" + """</p>
            <p><strong>总成交量:</strong> """ + f"{trade_data['quantity'].sum():.4f} BTCUSDT" + """</p>
        </div>
    """
    
    # 添加图表
    for i, (interval, fig) in enumerate(figures):
        html_content += f"""
        <div class="chart-container">
            <h2>{interval} 时间周期分析</h2>
            <div id="chart_{i}"></div>
            <script>
                Plotly.newPlot('chart_{i}', {fig.to_json()});
            </script>
        </div>
        """
    
    html_content += """
        <div class="stats">
            <h3>🔍 技术指标说明</h3>
            <ul>
                <li><strong>SMA(20):</strong> 20周期简单移动平均线，用于判断趋势方向</li>
                <li><strong>EMA(12):</strong> 12周期指数移动平均线，对价格变化更敏感</li>
                <li><strong>MACD:</strong> 移动平均收敛发散指标，用于判断买卖信号</li>
                <li><strong>RSI:</strong> 相对强弱指数，70以上超买，30以下超卖</li>
                <li><strong>成交量:</strong> 反映市场活跃度和趋势强度</li>
            </ul>
        </div>
    </body>
    </html>
    """
    
    # 保存HTML文件
    with open('binance_analysis_report.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ HTML报告已生成: binance_analysis_report.html")
    print("\n🎯 使用方法:")
    print("   在浏览器中打开 binance_analysis_report.html 查看完整的技术分析报告")
    print("   报告包含多时间周期的K线图、技术指标和交互式图表")

if __name__ == "__main__":
    create_analysis_report()
