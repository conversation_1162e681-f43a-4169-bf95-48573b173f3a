#!/usr/bin/env python3
"""
Check data stored in DolphinDB
"""

import dolphindb as ddb

def check_dolphindb_data():
    """Check what data is stored in DolphinDB."""
    try:
        # Connect to DolphinDB
        session = ddb.session()
        session.connect("localhost", 8848, "", "")
        print("✅ Connected to DolphinDB successfully")
        
        # Check kline data
        try:
            result = session.run("select count(*) from shared_kline_data")
            print(f"✅ Total records in kline table: {result}")

            # Get latest 5 kline records
            latest_data = session.run("select top 5 * from shared_kline_data order by timestamp desc")
            print(f"✅ Latest 5 kline records:")
            print(latest_data)

        except Exception as e:
            print(f"❌ Error querying kline data: {e}")
            print("This might mean no kline data has been saved yet or table doesn't exist")

        # Check aggressive trade data
        try:
            result = session.run("select count(*) from shared_agg_trade_data")
            print(f"✅ Total records in aggressive trade table: {result}")

            # Get latest 10 aggressive trade records
            latest_trades = session.run("select top 10 * from shared_agg_trade_data order by timestamp desc")
            print(f"✅ Latest 10 aggressive trade records:")
            print(latest_trades)

            # Get unique symbols
            symbols = session.run("select distinct symbol from shared_agg_trade_data")
            print(f"✅ Symbols in aggressive trade database: {symbols}")

        except Exception as e:
            print(f"❌ Error querying aggressive trade data: {e}")
            print("This might mean no aggressive trade data has been saved yet or table doesn't exist")
        
        session.close()
        
    except Exception as e:
        print(f"❌ Failed to connect to DolphinDB: {e}")

if __name__ == "__main__":
    check_dolphindb_data()
