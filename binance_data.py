#!/usr/bin/env python3
"""
Binance Futures Trading Bot
"""

import os
import json
import sys
import time
import threading
import csv
import logging
import argparse
from datetime import datetime
from dataclasses import dataclass
from typing import Optional, List, Dict, Any
import pytz
from binance.um_futures import UMFutures
from binance.websocket.um_futures.websocket_client import UMFuturesWebsocketClient
import dotenv
import dolphindb as ddb
import pandas as pd

dotenv.load_dotenv()


@dataclass
class TradingConfig:
    """Configuration class for trading parameters."""
    symbol: str = 'BTCUSDT'
    quantity: float = 0.01
    take_profit: float = 25
    direction: str = 'BUY'
    max_orders: int = 5
    wait_time: int = 30

    @property
    def close_order_side(self) -> str:
        """Get the close order side based on bot direction."""
        return 'BUY' if self.direction == "SELL" else 'SELL'


@dataclass
class OrderMonitor:
    """Thread-safe order monitoring state."""
    order_id: Optional[str] = None
    filled: bool = False
    filled_price: Optional[float] = None
    filled_qty: float = 0.0

    def reset(self):
        """Reset the monitor state."""
        self.order_id = None
        self.filled = False
        self.filled_price = None
        self.filled_qty = 0.0


class TradingLogger:
    """Enhanced logging with structured output and error handling."""

    def __init__(self, symbol: str, log_to_console: bool = False):
        self.symbol = symbol
        self.log_file = f"{symbol}_transactions_log.csv"
        self.debug_log_file = f"{symbol}_bot_activity.log"
        self.logger = self._setup_logger(log_to_console)
        # Get timezone from environment variable, default to UTC
        timezone_name = os.getenv('TIMEZONE', 'UTC')
        self.timezone = pytz.timezone(timezone_name)

    def _setup_logger(self, log_to_console: bool) -> logging.Logger:
        """Setup the logger with proper configuration."""
        logger = logging.getLogger(f"trading_bot_{self.symbol}")
        logger.setLevel(logging.INFO)

        # Prevent duplicate handlers
        if logger.handlers:
            return logger

        formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )

        # File handler
        file_handler = logging.FileHandler(self.debug_log_file, mode='a')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Console handler
        if log_to_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        return logger

    def log(self, message: str, level: str = "INFO"):
        """Log a message with the specified level."""
        getattr(self.logger, level.lower())(message)


class DolphinDBClient:
    """DolphinDB client for storing kline data."""

    def __init__(self, host: str = "localhost", port: int = 8848, username: str = "", password: str = ""):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session = None
        self.connect()
        self.setup_database()

    def connect(self):
        """Connect to DolphinDB server."""
        try:
            self.session = ddb.session()
            self.session.connect(self.host, self.port, self.username, self.password)
            print(f"Connected to DolphinDB at {self.host}:{self.port}")
        except Exception as e:
            print(f"Failed to connect to DolphinDB: {e}")
            raise

    def setup_database(self):
        """Setup tables for kline and aggressive trade data using in-memory tables."""
        try:
            # Check if kline table exists, create if not
            try:
                result = self.session.run("select count(*) from shared_kline_data")
                print(f"Kline table exists with {result.iloc[0,0]} records")
            except:
                # Table doesn't exist, create it
                self.session.run("""
                    kline_data = table(
                        1:0,
                        `symbol`timestamp`open_time`close_time`open_price`high_price`low_price`close_price`volume`quote_volume`trade_count`taker_buy_volume`taker_buy_quote_volume,
                        [SYMBOL, TIMESTAMP, TIMESTAMP, TIMESTAMP, DOUBLE, DOUBLE, DOUBLE, DOUBLE, DOUBLE, DOUBLE, INT, DOUBLE, DOUBLE]
                    )
                    share kline_data as shared_kline_data
                """)
                print("Created new kline data table")

            # Check if aggressive trade table exists, create if not
            try:
                result = self.session.run("select count(*) from shared_agg_trade_data_prd")
                print(f"Aggressive trade table exists with {result.iloc[0,0]} records")
            except:
                # Table doesn't exist, create it
                self.session.run("""
                    agg_trade_data = table(
                        1:0,
                        `symbol`timestamp`trade_id`price`quantity`first_trade_id`last_trade_id`trade_time`is_buyer_maker,
                        [SYMBOL, TIMESTAMP, LONG, DOUBLE, DOUBLE, LONG, LONG, TIMESTAMP, BOOL]
                    )
                    share agg_trade_data as shared_agg_trade_data_prd
                """)
                print("Created new aggressive trade data table")

            print("DolphinDB tables setup completed")
        except Exception as e:
            print(f"Failed to setup DolphinDB tables: {e}")
            raise

    def insert_kline_data(self, kline_data: Dict[str, Any]):
        """Insert kline data into DolphinDB."""
        try:
            # Extract kline information
            k = kline_data['k']

            # Convert timestamps from milliseconds to DolphinDB timestamp
            open_time = pd.to_datetime(k['t'], unit='ms')
            close_time = pd.to_datetime(k['T'], unit='ms')
            event_time = pd.to_datetime(kline_data['E'], unit='ms')

            # Prepare data for insertion
            data = {
                'symbol': [k['s']],
                'timestamp': [event_time],
                'open_time': [open_time],
                'close_time': [close_time],
                'open_price': [float(k['o'])],
                'high_price': [float(k['h'])],
                'low_price': [float(k['l'])],
                'close_price': [float(k['c'])],
                'volume': [float(k['v'])],
                'quote_volume': [float(k['q'])],
                'trade_count': [int(k['n'])],
                'taker_buy_volume': [float(k['V'])],
                'taker_buy_quote_volume': [float(k['Q'])]
            }

            # Create DataFrame and insert
            df = pd.DataFrame(data)
            self.session.upload({'new_kline_data': df})

            # Insert into shared table
            self.session.run("""
                shared_kline_data.append!(new_kline_data)
            """)

            print(f"Inserted kline data for {k['s']} at {event_time}")

        except Exception as e:
            print(f"Failed to insert kline data: {e}")

    def insert_agg_trade_data(self, trade_data: Dict[str, Any]):
        """Insert aggressive trade data into DolphinDB."""
        try:
            # Convert timestamps from milliseconds to DolphinDB timestamp
            event_time = pd.to_datetime(trade_data['E'], unit='ms')
            trade_time = pd.to_datetime(trade_data['T'], unit='ms')

            # Prepare data for insertion
            data = {
                'symbol': [trade_data['s']],
                'timestamp': [event_time],
                'trade_id': [int(trade_data['a'])],
                'price': [float(trade_data['p'])],
                'quantity': [float(trade_data['q'])],
                'first_trade_id': [int(trade_data['f'])],
                'last_trade_id': [int(trade_data['l'])],
                'trade_time': [trade_time],
                'is_buyer_maker': [bool(trade_data['m'])]
            }

            # Create DataFrame and insert
            df = pd.DataFrame(data)
            self.session.upload({'new_agg_trade_data': df})

            # Insert into shared table
            self.session.run("""
                shared_agg_trade_data_prd.append!(new_agg_trade_data)
            """)

            print(f"Inserted aggressive trade data for {trade_data['s']}: {trade_data['q']} @ {trade_data['p']}")

        except Exception as e:
            print(f"Failed to insert aggressive trade data: {e}")

    def close(self):
        """Close DolphinDB connection."""
        if self.session:
            self.session.close()
            print("DolphinDB connection closed")


class BinanceClient:
    """Enhanced Binance client with retry logic and error handling."""

    def __init__(self, api_key: str, api_secret: str):
        self.client = UMFutures(key=api_key, secret=api_secret)
        self._listen_key = None
        self._ws_client = None

    def get_listen_key(self) -> str:
        """Get or refresh the listen key."""
        if not self._listen_key:
            self._listen_key = self.client.new_listen_key()["listenKey"]
        return self._listen_key

    def renew_listen_key(self):
        """Renew the listen key."""
        try:
            self.client.renew_listen_key(listenKey=self._listen_key)
        except Exception:
            # If renewal fails, get a new key
            self._listen_key = None
            self.get_listen_key()

class WebSocketManager:
    """Manages WebSocket connections with automatic reconnection."""

    def __init__(self, client: BinanceClient, logger: TradingLogger,
                 order_monitor: OrderMonitor, config: TradingConfig, fill_event: threading.Event,
                 dolphindb_client: DolphinDBClient = None):
        self.client = client
        self.logger = logger
        self.order_monitor = order_monitor
        self.config = config
        self.fill_event = fill_event
        self.dolphindb_client = dolphindb_client
        self.ws_client = None
        self.cumulative_pnl = 0.0
        self.start_time = time.time()
        self._running = False

    def start(self):
        """Start the WebSocket connection."""
        self._running = True
        listen_key = self.client.get_listen_key()
        self.ws_client = UMFuturesWebsocketClient(on_message=self._handle_message, is_combined=True)
        self.ws_client.agg_trade(symbol="BTCUSDT") # 订阅实时成交
        self.ws_client.agg_trade(symbol="ETHUSDT") # 订阅实时成交
        # self.ws_client.user_data(listen_key=listen_key)
        # self.ws_client.mark_price(symbol="BTCUSDT", speed=5000)
        # self.ws_client.kline(symbol="BTCUSDT", interval="1m")

    def stop(self):
        """Stop the WebSocket connection."""
        self._running = False
        if self.ws_client:
            self.ws_client.stop()

    def _handle_message(self, _, raw_msg: str):
        """Handle incoming WebSocket messages."""
        try:
            msg = json.loads(raw_msg)
            # print(f"Parsed message-------------: {msg}")

            # Handle kline data from stream
            if 'stream' in msg and 'kline' in msg['stream'] and 'data' in msg:
                self._handle_kline_data(msg['data'])
            # Handle aggressive trade data from stream
            elif 'stream' in msg and 'aggTrade' in msg['stream'] and 'data' in msg:
                self._handle_agg_trade_data(msg['data'])
            # Handle direct kline data
            elif 'e' in msg and msg['e'] == 'kline':
                self._handle_kline_data(msg)
            # Handle direct aggressive trade data
            elif 'e' in msg and msg['e'] == 'aggTrade':
                self._handle_agg_trade_data(msg)

        except Exception as e:
            self.logger.log(f"Error handling WebSocket message: {e}", "ERROR")

    def _handle_kline_data(self, msg: Dict[str, Any]):
        """Handle kline data and save to DolphinDB."""
        try:
            kline = msg['k']
            symbol = kline['s']
            is_closed = kline['x']  # Whether this kline is closed

            # Only save closed klines to avoid duplicates
            if is_closed and self.dolphindb_client:
                print(f"Saving closed kline for {symbol}")
                self.dolphindb_client.insert_kline_data(msg)
            elif is_closed:
                print(f"Kline closed for {symbol} but no DolphinDB client available")
            else:
                print(f"Kline update for {symbol} (not closed yet)")

        except Exception as e:
            self.logger.log(f"Error handling kline data: {e}", "ERROR")

    def _handle_agg_trade_data(self, msg: Dict[str, Any]):
        """Handle aggressive trade data and save to DolphinDB."""
        try:
            symbol = msg['s']

            # Save every aggressive trade to DolphinDB
            if self.dolphindb_client:
                self.dolphindb_client.insert_agg_trade_data(msg)
            else:
                print(f"Aggressive trade for {symbol}: {msg['q']} @ {msg['p']} (no DolphinDB client)")

        except Exception as e:
            self.logger.log(f"Error handling aggressive trade data: {e}", "ERROR")




class TradingBot:
    """Main trading bot class with all trading logic."""

    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = TradingLogger(config.symbol)
        self.order_monitor = OrderMonitor()
        self.fill_event = threading.Event()

        # Validate API credentials
        api_key = os.getenv("API_KEY")
        api_secret = os.getenv("API_SECRET")

        if not api_key or not api_secret:
            raise ValueError("API_KEY and API_SECRET environment variables must be set")

        # Initialize DolphinDB client
        try:
            self.dolphindb_client = DolphinDBClient()
            print("DolphinDB client initialized successfully")
        except Exception as e:
            print(f"Failed to initialize DolphinDB client: {e}")
            self.dolphindb_client = None

        self.client = BinanceClient(api_key, api_secret)
        self.ws_manager = WebSocketManager(self.client, self.logger,
                                           self.order_monitor, self.config, self.fill_event,
                                           self.dolphindb_client)

        # Trading state
        self.last_open_order_time = 0
        self.skip_waiting_time = False
        self.close_order = {"orderId": None}
        self.order_status = None
        self.active_close_orders = []
        self.last_log_time = time.time()

    def _refresh_listen_key_thread(self):
        """Background thread to refresh the listen key."""
        while True:
            try:
                time.sleep(30 * 60)  # 30 minutes
                self.client.renew_listen_key()
                self.logger.log("Refreshed listenKey")
            except Exception as e:
                self.logger.log(f"Error renewing listen key: {e}", "ERROR")



    def run(self):
        """Main trading loop."""
        try:
            # Start WebSocket
            self.ws_manager.start()

            # Start refresh listen key thread
            refresh_listen_key_thread = threading.Thread(
                target=self._refresh_listen_key_thread, daemon=True
            )
            refresh_listen_key_thread.start()

            # Main trading loop
            while True:
                time.sleep(10)

        except KeyboardInterrupt:
            self.logger.log("Bot stopped by user")
        except Exception as e:
            self.logger.log(f"Critical error: {e}", "ERROR")
            import traceback
            self.logger.log(traceback.format_exc(), "ERROR")
            raise
        finally:
            self.ws_manager.stop()
            if self.dolphindb_client:
                self.dolphindb_client.close()


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Binance Futures Trading Bot')
    parser.add_argument('--symbol', type=str, default='BTCUSDT',
                        help='Trading pair symbol (default: BTCUSDT)')
    parser.add_argument('--quantity', type=float, default=0.02,
                        help='Order quantity (default: 1)')
    parser.add_argument('--take-profit', type=float, default=300,
                        help='Take profit in USDC (default: 1)')
    parser.add_argument('--direction', type=str, default='BUY',
                        help='Direction of the bot (default: BUY)')
    parser.add_argument('--max-orders', type=int, default=1,
                        help='Maximum number of active orders (default: 5)')
    parser.add_argument('--wait-time', type=int, default=30,
                        help='Wait time between orders in seconds (default: 300)')
    return parser.parse_args()


def main():
    """Main entry point."""
    args = parse_arguments()

    # Create configuration
    config = TradingConfig(
        symbol=args.symbol,
        quantity=args.quantity,
        take_profit=args.take_profit,
        direction=args.direction,
        max_orders=args.max_orders,
        wait_time=args.wait_time
    )

    # Create and run the bot
    bot = TradingBot(config)
    bot.run()


if __name__ == "__main__":
    main()
